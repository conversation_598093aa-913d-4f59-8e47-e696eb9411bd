/**
 * وحدة العروض والخصومات
 * Promotions Module
 */

export class Promotions {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;

        this.promotions = [];
        this.currentPromotion = null;
    }

    /**
     * عرض واجهة العروض
     */
    async render() {
        try {
            await this.loadData();
            this.createPromotionsInterface();
            this.bindEvents();
            this.loadPromotionsList();
        } catch (error) {
            console.error('خطأ في عرض واجهة العروض:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة العروض');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        this.promotions = this.storage.get('promotions') || [];
    }

    /**
     * إنشاء واجهة العروض
     */
    createPromotionsInterface() {
        const container = document.getElementById('promotions-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="promotions-container">
                <div class="promotions-header neumorphic-card">
                    <h2><i class="bi bi-tags"></i> العروض والخصومات</h2>
                    <div class="promotions-actions">
                        <button class="btn neumorphic-btn btn-primary" onclick="promotionsModule.showAddPromotionModal()">
                            <i class="bi bi-plus-circle"></i>
                            إضافة عرض جديد
                        </button>
                    </div>
                </div>

                <div class="promotions-content">
                    <div class="promotions-list" id="promotionsList">
                        <!-- Promotions will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Add/Edit Promotion Modal -->
            <div class="modal fade" id="promotionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content neumorphic-modal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="promotionModalTitle">
                                <i class="bi bi-plus-circle"></i>
                                إضافة عرض جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="promotionForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم العرض *</label>
                                    <input type="text" id="promotionName" class="form-control neumorphic-input" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea id="promotionDescription" class="form-control neumorphic-input" rows="3"></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الخصم</label>
                                            <select id="discountType" class="form-select neumorphic-select">
                                                <option value="percentage">نسبة مئوية</option>
                                                <option value="fixed">مبلغ ثابت</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">قيمة الخصم *</label>
                                            <input type="number" id="discountValue" class="form-control neumorphic-input"
                                                   step="0.01" min="0" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ البداية</label>
                                            <input type="date" id="startDate" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ النهاية</label>
                                            <input type="date" id="endDate" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="promotionActive" class="form-check-input neumorphic-checkbox" checked>
                                    <label class="form-check-label" for="promotionActive">
                                        عرض نشط
                                    </label>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary neumorphic-btn" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-primary neumorphic-btn" onclick="promotionsModule.savePromotion()">
                                <i class="bi bi-check-circle"></i>
                                حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        window.promotionsModule = this;
    }

    /**
     * تحميل قائمة العروض
     */
    loadPromotionsList() {
        const container = document.getElementById('promotionsList');
        if (!container) return;

        if (this.promotions.length === 0) {
            container.innerHTML = `
                <div class="empty-promotions">
                    <i class="bi bi-tags"></i>
                    <p>لا توجد عروض</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.promotions.map(promotion => `
            <div class="promotion-card neumorphic-card">
                <div class="promotion-header">
                    <h5>${promotion.name}</h5>
                    <span class="badge ${promotion.isActive ? 'badge-success' : 'badge-secondary'}">
                        ${promotion.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </div>
                <div class="promotion-body">
                    <p>${promotion.description || 'لا يوجد وصف'}</p>
                    <div class="promotion-details">
                        <span>الخصم: ${promotion.discountValue}${promotion.discountType === 'percentage' ? '%' : ' ريال'}</span>
                        <span>من: ${this.utils.formatDate(promotion.startDate)}</span>
                        <span>إلى: ${this.utils.formatDate(promotion.endDate)}</span>
                    </div>
                </div>
                <div class="promotion-actions">
                    <button class="btn btn-sm neumorphic-btn" onclick="promotionsModule.editPromotion(${promotion.id})">
                        <i class="bi bi-pencil"></i>
                        تعديل
                    </button>
                    <button class="btn btn-sm neumorphic-btn btn-danger" onclick="promotionsModule.deletePromotion(${promotion.id})">
                        <i class="bi bi-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * عرض نافذة إضافة عرض
     */
    showAddPromotionModal() {
        this.currentPromotion = null;
        document.getElementById('promotionModalTitle').innerHTML = '<i class="bi bi-plus-circle"></i> إضافة عرض جديد';
        this.resetPromotionForm();

        const modal = new bootstrap.Modal(document.getElementById('promotionModal'));
        modal.show();
    }

    /**
     * إعادة تعيين نموذج العرض
     */
    resetPromotionForm() {
        document.getElementById('promotionForm').reset();
        document.getElementById('promotionActive').checked = true;
    }

    /**
     * حفظ العرض
     */
    async savePromotion() {
        try {
            const formData = this.getPromotionFormData();

            if (!this.validatePromotionData(formData)) {
                return;
            }

            if (this.currentPromotion) {
                await this.updatePromotion(formData);
            } else {
                await this.addPromotion(formData);
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('promotionModal'));
            modal.hide();

            this.loadPromotionsList();

        } catch (error) {
            console.error('خطأ في حفظ العرض:', error);
            this.notifications.error('حدث خطأ في حفظ العرض');
        }
    }

    /**
     * الحصول على بيانات النموذج
     */
    getPromotionFormData() {
        return {
            name: document.getElementById('promotionName').value.trim(),
            description: document.getElementById('promotionDescription').value.trim(),
            discountType: document.getElementById('discountType').value,
            discountValue: parseFloat(document.getElementById('discountValue').value) || 0,
            startDate: document.getElementById('startDate').value,
            endDate: document.getElementById('endDate').value,
            isActive: document.getElementById('promotionActive').checked
        };
    }

    /**
     * التحقق من صحة البيانات
     */
    validatePromotionData(data) {
        if (!data.name) {
            this.notifications.error('اسم العرض مطلوب');
            return false;
        }

        if (data.discountValue <= 0) {
            this.notifications.error('قيمة الخصم يجب أن تكون أكبر من صفر');
            return false;
        }

        return true;
    }

    /**
     * إضافة عرض جديد
     */
    async addPromotion(data) {
        const newPromotion = {
            id: this.generatePromotionId(),
            ...data,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.promotions.push(newPromotion);
        this.storage.set('promotions', this.promotions);

        this.notifications.success('تم إضافة العرض بنجاح');
    }

    /**
     * تحديث عرض موجود
     */
    async updatePromotion(data) {
        const index = this.promotions.findIndex(p => p.id === this.currentPromotion.id);
        if (index !== -1) {
            this.promotions[index] = {
                ...this.currentPromotion,
                ...data,
                updatedAt: new Date().toISOString()
            };

            this.storage.set('promotions', this.promotions);
            this.notifications.success('تم تحديث العرض بنجاح');
        }
    }

    /**
     * توليد معرف عرض جديد
     */
    generatePromotionId() {
        const maxId = this.promotions.reduce((max, promotion) => Math.max(max, promotion.id || 0), 0);
        return maxId + 1;
    }

    /**
     * تعديل عرض
     */
    editPromotion(promotionId) {
        const promotion = this.promotions.find(p => p.id === promotionId);
        if (!promotion) return;

        this.currentPromotion = promotion;
        document.getElementById('promotionModalTitle').innerHTML = '<i class="bi bi-pencil"></i> تعديل العرض';
        this.fillPromotionForm(promotion);

        const modal = new bootstrap.Modal(document.getElementById('promotionModal'));
        modal.show();
    }

    /**
     * ملء نموذج العرض
     */
    fillPromotionForm(promotion) {
        document.getElementById('promotionName').value = promotion.name;
        document.getElementById('promotionDescription').value = promotion.description || '';
        document.getElementById('discountType').value = promotion.discountType;
        document.getElementById('discountValue').value = promotion.discountValue;
        document.getElementById('startDate').value = promotion.startDate;
        document.getElementById('endDate').value = promotion.endDate;
        document.getElementById('promotionActive').checked = promotion.isActive;
    }

    /**
     * حذف عرض
     */
    async deletePromotion(promotionId) {
        const promotion = this.promotions.find(p => p.id === promotionId);
        if (!promotion) return;

        const confirmed = await this.notifications.confirm(
            `هل تريد حذف العرض "${promotion.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const index = this.promotions.findIndex(p => p.id === promotionId);
            if (index !== -1) {
                this.promotions.splice(index, 1);
                this.storage.set('promotions', this.promotions);
                this.loadPromotionsList();
                this.notifications.success('تم حذف العرض بنجاح');
            }
        }
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadPromotionsList();
    }
}