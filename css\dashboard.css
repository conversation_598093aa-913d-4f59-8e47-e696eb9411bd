/* ===== Dashboard Specific Styles ===== */

.dashboard-container {
    padding: 2rem;
    background: var(--neu-bg);
    min-height: 100vh;
}

/* ===== Stats Cards ===== */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    position: relative;
    overflow: hidden;
    transition: var(--neu-transition);
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--neu-primary), var(--neu-info));
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--neu-shadow-hover), 0 15px 30px rgba(52, 152, 219, 0.2);
}

.stat-card:active {
    transform: translateY(-4px);
}

.stat-icon {
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: var(--neu-transition);
    opacity: 0;
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

.stat-info h3 {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neu-text), var(--neu-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== Charts Section ===== */
.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.chart-container {
    position: relative;
    height: 400px;
    transition: var(--neu-transition);
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: var(--neu-shadow-hover);
}

.chart-container h4 {
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.chart-container h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--neu-primary), var(--neu-info));
    border-radius: 2px;
}

.chart-container canvas {
    max-height: 300px;
}

/* ===== Recent Activities ===== */
.dashboard-recent {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.recent-sales,
.low-stock {
    position: relative;
    max-height: 500px;
    overflow: hidden;
}

.recent-sales h4,
.low-stock h4 {
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.recent-sales h4::after,
.low-stock h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--neu-success), var(--neu-warning));
    border-radius: 2px;
}

.recent-list,
.stock-list {
    max-height: 350px;
    overflow-y: auto;
    padding-left: 0.5rem;
}

.recent-list::-webkit-scrollbar,
.stock-list::-webkit-scrollbar {
    width: 6px;
}

.recent-list::-webkit-scrollbar-track,
.stock-list::-webkit-scrollbar-track {
    background: var(--neu-bg);
    border-radius: 3px;
}

.recent-list::-webkit-scrollbar-thumb,
.stock-list::-webkit-scrollbar-thumb {
    background: var(--neu-shadow-dark);
    border-radius: 3px;
}

.recent-item,
.stock-item {
    position: relative;
    border-radius: var(--neu-radius-sm);
    margin-bottom: 1rem;
    transition: var(--neu-transition);
    border: 1px solid transparent;
}

.recent-item:hover,
.stock-item:hover {
    border-color: rgba(52, 152, 219, 0.3);
    box-shadow: var(--neu-shadow-hover);
}

.recent-item::before,
.stock-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--neu-primary), var(--neu-info));
    border-radius: 0 var(--neu-radius-sm) var(--neu-radius-sm) 0;
    opacity: 0;
    transition: var(--neu-transition);
}

.recent-item:hover::before,
.stock-item:hover::before {
    opacity: 1;
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 1rem;
    margin-bottom: 0.25rem;
    transition: var(--neu-transition);
}

.recent-item:hover .item-name,
.stock-item:hover .item-name {
    color: var(--neu-primary);
}

.item-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.item-value {
    font-weight: 600;
    white-space: nowrap;
}

/* ===== Empty States ===== */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--neu-text-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* ===== Loading States ===== */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    flex-direction: column;
    gap: 1rem;
}

.chart-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(52, 152, 219, 0.2);
    border-top: 4px solid var(--neu-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.chart-loading p {
    color: var(--neu-text-light);
    margin: 0;
}

/* ===== Animations ===== */
@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-info h3 {
    animation: countUp 0.6s ease;
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .chart-container {
        height: 350px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-info h3 {
        font-size: 1.5rem;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .chart-container {
        height: 300px;
        padding: 1.5rem;
    }
    
    .dashboard-recent {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .recent-sales,
    .low-stock {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0.5rem;
    }
    
    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto;
    }
    
    .chart-container {
        height: 250px;
        padding: 1rem;
    }
    
    .recent-sales,
    .low-stock {
        padding: 1rem;
    }
    
    .recent-item,
    .stock-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
    .chart-container canvas {
        filter: brightness(0.9);
    }
}

/* ===== Print Styles ===== */
@media print {
    .dashboard-container {
        background: white;
        color: black;
    }
    
    .stat-card,
    .chart-container,
    .recent-sales,
    .low-stock {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
    
    .chart-container {
        height: auto;
    }
}
