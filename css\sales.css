/**
 * تنسيقات المبيعات
 * Sales Styles
 */

/* الحاوية الرئيسية للمبيعات */
.sales-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    height: calc(100vh - 120px);
    padding: 1rem;
}

/* قسم المنتجات والبحث */
.products-section {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
    padding: 1.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.sales-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--neu-border);
}

.sales-header h2 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sales-search {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--neu-radius);
    background: var(--neu-bg-light);
    box-shadow: var(--neu-shadow-inset);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.barcode-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--neu-radius);
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow);
    color: var(--neu-text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.barcode-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

/* قائمة المنتجات */
.products-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.products-list::-webkit-scrollbar {
    width: 6px;
}

.products-list::-webkit-scrollbar-track {
    background: var(--neu-bg-light);
    border-radius: 3px;
}

.products-list::-webkit-scrollbar-thumb {
    background: var(--neu-primary);
    border-radius: 3px;
}

.product-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-item:hover {
    background: var(--neu-bg-hover);
    transform: translateX(-3px);
}

.product-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--neu-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.product-info {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--neu-text-light);
}

.product-price {
    font-weight: 600;
    color: var(--neu-primary);
    font-size: 1rem;
}

.product-stock {
    font-size: 0.8rem;
    color: var(--neu-text-light);
}

.product-stock.low {
    color: #f39c12;
    font-weight: 600;
}

.product-stock.out {
    color: #e74c3c;
    font-weight: 600;
}

/* قسم الفاتورة */
.invoice-section {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.invoice-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--neu-border);
}

.invoice-header h3 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.invoice-number {
    font-size: 0.9rem;
    color: var(--neu-text-light);
    background: var(--neu-bg-light);
    padding: 0.25rem 0.5rem;
    border-radius: var(--neu-radius-sm);
}

/* عناصر الفاتورة */
.invoice-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1.5rem;
    max-height: 300px;
}

.invoice-items::-webkit-scrollbar {
    width: 6px;
}

.invoice-items::-webkit-scrollbar-track {
    background: var(--neu-bg-light);
    border-radius: 3px;
}

.invoice-items::-webkit-scrollbar-thumb {
    background: var(--neu-primary);
    border-radius: 3px;
}

.invoice-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    transition: all 0.3s ease;
}

.invoice-item:hover {
    background: var(--neu-bg-hover);
}

.item-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--neu-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-price {
    color: var(--neu-text-light);
    font-size: 0.8rem;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow);
    color: var(--neu-text);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.qty-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--neu-shadow-hover);
}

.qty-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-inset);
}

.qty-display {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--neu-text);
}

.remove-item-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: #e74c3c;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.remove-item-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.item-total {
    font-weight: 600;
    color: var(--neu-primary);
    min-width: 80px;
    text-align: right;
}

/* ملخص الفاتورة */
.invoice-summary {
    padding: 1.5rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    margin-bottom: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.summary-row.total {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--neu-primary);
    border-top: 1px solid var(--neu-border);
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

.summary-label {
    color: var(--neu-text);
}

.summary-value {
    font-weight: 600;
    color: var(--neu-text);
}

/* أزرار الإجراءات */
.invoice-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.action-btn {
    padding: 1rem;
    border: none;
    border-radius: var(--neu-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.clear-invoice-btn {
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow);
    color: var(--neu-text);
}

.clear-invoice-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.checkout-btn {
    background: var(--neu-primary);
    box-shadow: var(--neu-shadow);
    color: white;
}

.checkout-btn:hover:not(:disabled) {
    background: var(--neu-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.checkout-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* الفاتورة الفارغة */
.empty-invoice {
    text-align: center;
    padding: 2rem;
    color: var(--neu-text-light);
}

.empty-invoice i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-invoice p {
    margin: 0;
    font-size: 1.1rem;
}

/* المنتجات الفارغة */
.empty-products {
    text-align: center;
    padding: 3rem;
    color: var(--neu-text-light);
}

.empty-products i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-products p {
    margin: 0;
    font-size: 1.2rem;
}

/* التجاوب */
@media (max-width: 1024px) {
    .sales-container {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
        height: calc(100vh - 100px);
    }
    
    .invoice-section {
        max-height: 400px;
    }
}

@media (max-width: 768px) {
    .sales-container {
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .sales-search {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .invoice-actions {
        grid-template-columns: 1fr;
    }
    
    .action-btn {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .product-item,
    .invoice-item {
        padding: 0.75rem;
    }
    
    .product-image,
    .item-image {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}
