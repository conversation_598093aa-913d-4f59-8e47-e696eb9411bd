const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات التطبيق
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    getPlatform: () => ipcRenderer.invoke('get-platform'),
    
    // حوارات الملفات
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    
    // عمليات الملفات
    writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
    
    // أحداث القائمة
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-new', callback);
        ipcRenderer.on('menu-open', callback);
        ipcRenderer.on('menu-save', callback);
        ipcRenderer.on('menu-export-data', callback);
        ipcRenderer.on('menu-export-reports', callback);
    },
    
    // إزالة المستمعين
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
    
    // التحقق من كون التطبيق يعمل في Electron
    isElectron: true,
    
    // دوال مساعدة للنسخ الاحتياطية
    backup: {
        export: async (data, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'ملفات النسخ الاحتياطية', extensions: ['json'] },
                    { name: 'جميع الملفات', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled) {
                return await ipcRenderer.invoke('write-file', result.filePath, data);
            }
            return { canceled: true };
        },
        
        import: async () => {
            const result = await ipcRenderer.invoke('show-open-dialog', {
                properties: ['openFile'],
                filters: [
                    { name: 'ملفات النسخ الاحتياطية', extensions: ['json'] },
                    { name: 'جميع الملفات', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled) {
                return await ipcRenderer.invoke('read-file', result.filePaths[0]);
            }
            return { canceled: true };
        }
    },
    
    // دوال التقارير
    reports: {
        exportPDF: async (htmlContent, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'ملفات PDF', extensions: ['pdf'] },
                    { name: 'جميع الملفات', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled) {
                // سيتم تنفيذ تصدير PDF في الإصدارات المستقبلية
                return { success: true, path: result.filePath };
            }
            return { canceled: true };
        },
        
        exportExcel: async (data, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'ملفات Excel', extensions: ['xlsx', 'xls'] },
                    { name: 'ملفات CSV', extensions: ['csv'] },
                    { name: 'جميع الملفات', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled) {
                // تحويل البيانات إلى CSV كحل مؤقت
                const csvData = this.convertToCSV(data);
                return await ipcRenderer.invoke('write-file', result.filePath, csvData);
            }
            return { canceled: true };
        },
        
        convertToCSV: (data) => {
            if (!Array.isArray(data) || data.length === 0) return '';
            
            const headers = Object.keys(data[0]);
            const csvHeaders = headers.join(',');
            const csvRows = data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            );
            
            return [csvHeaders, ...csvRows].join('\n');
        }
    },
    
    // دوال الطباعة
    print: {
        receipt: async (content) => {
            // سيتم تنفيذ الطباعة في الإصدارات المستقبلية
            console.log('طباعة الفاتورة:', content);
            return { success: true };
        },
        
        report: async (content) => {
            // سيتم تنفيذ طباعة التقارير في الإصدارات المستقبلية
            console.log('طباعة التقرير:', content);
            return { success: true };
        }
    },
    
    // دوال النظام
    system: {
        getSystemInfo: async () => {
            const platform = await ipcRenderer.invoke('get-platform');
            const version = await ipcRenderer.invoke('get-app-version');
            
            return {
                platform,
                version,
                arch: process.arch,
                nodeVersion: process.versions.node,
                electronVersion: process.versions.electron,
                chromeVersion: process.versions.chrome
            };
        },
        
        openExternal: (url) => {
            // سيتم تنفيذ فتح الروابط الخارجية
            console.log('فتح رابط خارجي:', url);
        }
    }
});

// إضافة دوال مساعدة للتطبيق
contextBridge.exposeInMainWorld('posUtils', {
    // التحقق من البيئة
    isDesktop: () => true,
    isWeb: () => false,
    
    // دوال التخزين المحسنة
    storage: {
        setItem: (key, value) => {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                return false;
            }
        },
        
        getItem: (key, defaultValue = null) => {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('خطأ في قراءة البيانات:', error);
                return defaultValue;
            }
        },
        
        removeItem: (key) => {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('خطأ في حذف البيانات:', error);
                return false;
            }
        },
        
        clear: () => {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('خطأ في مسح البيانات:', error);
                return false;
            }
        },
        
        getSize: () => {
            let total = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    total += localStorage[key].length + key.length;
                }
            }
            return total;
        }
    },
    
    // دوال الإشعارات المحسنة
    notifications: {
        show: (title, body, options = {}) => {
            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    return new Notification(title, { body, ...options });
                } else if (Notification.permission !== 'denied') {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            return new Notification(title, { body, ...options });
                        }
                    });
                }
            }
            return null;
        },
        
        requestPermission: () => {
            if ('Notification' in window) {
                return Notification.requestPermission();
            }
            return Promise.resolve('denied');
        }
    }
});

// تسجيل أحداث النافذة
window.addEventListener('DOMContentLoaded', () => {
    // إضافة فئة للإشارة إلى أن التطبيق يعمل في Electron
    document.body.classList.add('electron-app');
    
    // تعطيل القائمة السياقية الافتراضية
    document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
    
    // تعطيل اختصارات لوحة المفاتيح غير المرغوب فيها
    document.addEventListener('keydown', (e) => {
        // تعطيل F12 (أدوات المطور) في الإنتاج
        if (e.key === 'F12' && process.env.NODE_ENV !== 'development') {
            e.preventDefault();
        }
        
        // تعطيل Ctrl+Shift+I (أدوات المطور) في الإنتاج
        if (e.ctrlKey && e.shiftKey && e.key === 'I' && process.env.NODE_ENV !== 'development') {
            e.preventDefault();
        }
        
        // تعطيل Ctrl+U (عرض المصدر)
        if (e.ctrlKey && e.key === 'u') {
            e.preventDefault();
        }
    });
});

console.log('Preload script loaded successfully');
