/* ===== Main Application Styles ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--neu-bg);
    color: var(--neu-text);
    direction: rtl;
    overflow-x: hidden;
}

/* ===== Loading Screen ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--neu-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-container {
    text-align: center;
    animation: fadeInUp 0.8s ease;
}

.loading-logo {
    font-size: 4rem;
    color: var(--neu-primary);
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 2rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(52, 152, 219, 0.2);
    border-top: 4px solid var(--neu-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

/* ===== Navigation ===== */
.neumorphic-navbar {
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    padding: 1rem 0;
    border-bottom: none;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--neu-text);
    text-decoration: none;
}

.navbar-brand i {
    font-size: 1.5rem;
    color: var(--neu-primary);
}

.brand-text {
    background: linear-gradient(45deg, var(--neu-primary), var(--neu-info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--neu-text);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: var(--neu-radius);
    transition: var(--neu-transition);
}

.user-menu:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--neu-primary);
}

.user-menu i {
    font-size: 1.25rem;
}

/* ===== Main Layout ===== */
.main-content {
    display: flex;
    min-height: calc(100vh - 80px);
}

/* ===== Sidebar ===== */
.neumorphic-sidebar {
    width: 280px;
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    padding: 1.5rem 0;
    transition: var(--neu-transition);
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: var(--neu-radius);
    color: var(--neu-text);
    cursor: pointer;
    transition: var(--neu-transition);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.menu-item:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--neu-primary);
    transform: translateX(-2px);
}

.menu-item.active {
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-primary);
    font-weight: 600;
}

.menu-item i {
    font-size: 1.25rem;
    width: 20px;
    text-align: center;
}

.menu-item span {
    font-size: 1rem;
}

/* ===== Content Area ===== */
.content-area {
    flex: 1;
    padding: 2rem;
    background: var(--neu-bg);
    overflow-y: auto;
}

.tab-content {
    animation: fadeIn 0.3s ease;
}

.tab-content.d-none {
    display: none !important;
}

/* ===== Dashboard Styles ===== */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.dashboard-header h2 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 2rem;
    font-weight: 700;
    color: var(--neu-text);
}

.dashboard-header i {
    color: var(--neu-primary);
}

.dashboard-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--neu-text-light);
    font-size: 1rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    transition: var(--neu-transition);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(45deg, var(--neu-primary), var(--neu-info));
    box-shadow: var(--neu-shadow-outset);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--neu-text);
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: var(--neu-text-light);
    font-size: 1rem;
    margin: 0;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    padding: 2rem;
}

.chart-container h4 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 1.5rem;
}

.chart-container i {
    color: var(--neu-primary);
}

.dashboard-recent {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.recent-sales,
.low-stock {
    padding: 2rem;
}

.recent-sales h4,
.low-stock h4 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 1.5rem;
}

.recent-list,
.stock-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recent-item,
.stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(52, 152, 219, 0.05);
    border-radius: var(--neu-radius-sm);
    transition: var(--neu-transition-fast);
}

.recent-item:hover,
.stock-item:hover {
    background: rgba(52, 152, 219, 0.1);
    transform: translateX(-2px);
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-name {
    font-weight: 600;
    color: var(--neu-text);
}

.item-details {
    font-size: 0.875rem;
    color: var(--neu-text-light);
}

.item-value {
    font-weight: 600;
    color: var(--neu-primary);
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .neumorphic-sidebar {
        width: 100%;
        position: fixed;
        top: 80px;
        left: -100%;
        height: calc(100vh - 80px);
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .neumorphic-sidebar.show {
        left: 0;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .dashboard-header h2 {
        font-size: 1.5rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-info h3 {
        font-size: 1.5rem;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        padding: 1.5rem;
    }
    
    .dashboard-recent {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .content-area {
        padding: 0.5rem;
    }
    
    .dashboard-header h2 {
        font-size: 1.25rem;
    }
    
    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .recent-sales,
    .low-stock {
        padding: 1rem;
    }
}

/* ===== Animations ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* ===== Scrollbar Styling ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--neu-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--neu-shadow-dark);
    border-radius: 4px;
    transition: var(--neu-transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--neu-primary);
}
