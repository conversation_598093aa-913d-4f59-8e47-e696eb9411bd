/**
 * تنسيقات المخزون
 * Inventory Styles
 */

/* الحاوية الرئيسية */
.inventory-container {
    padding: 1rem;
}

.inventory-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.inventory-header h2 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.inventory-actions {
    display: flex;
    gap: 1rem;
}

/* تصفية المخزون */
.inventory-filters {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.filter-controls {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    align-items: end;
}

.search-group {
    position: relative;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 0.5rem;
    color: var(--neu-text);
    font-weight: 500;
}

/* جدول المنتجات */
.products-table-container {
    margin-bottom: 2rem;
    padding: 1.5rem;
    overflow: hidden;
}

.table-responsive {
    overflow-x: auto;
    border-radius: var(--neu-radius);
}

.neumorphic-table {
    width: 100%;
    margin: 0;
    background: var(--neu-bg);
    border-collapse: separate;
    border-spacing: 0;
}

.neumorphic-table thead th {
    background: var(--neu-bg-light);
    color: var(--neu-text);
    font-weight: 600;
    padding: 1rem;
    text-align: right;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.neumorphic-table tbody td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid var(--neu-border);
    color: var(--neu-text);
    vertical-align: middle;
}

.neumorphic-table tbody tr {
    transition: all 0.3s ease;
}

.neumorphic-table tbody tr:hover {
    background: var(--neu-bg-hover);
}

/* عناصر الجدول */
.product-image-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--neu-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.product-name {
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.25rem;
}

.neumorphic-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--neu-radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    background: var(--neu-bg-light);
    color: var(--neu-text);
    border: 1px solid var(--neu-border);
}

.badge-success {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border-color: #27ae60;
}

.badge-secondary {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
    border-color: #95a5a6;
}

.stock-info {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--neu-radius-sm);
    font-size: 0.9rem;
}

.stock-info.in-stock {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.stock-info.low-stock {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.stock-info.out-of-stock {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.5rem;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-danger {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
    background: rgba(231, 76, 60, 0.2);
    transform: translateY(-1px);
}

/* نافذة المنتج */
.neumorphic-modal .modal-content {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-modal);
}

.neumorphic-modal .modal-header {
    background: var(--neu-bg-light);
    border-bottom: 1px solid var(--neu-border);
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
}

.neumorphic-modal .modal-title {
    color: var(--neu-text);
    font-weight: 600;
}

.neumorphic-modal .modal-body {
    padding: 2rem;
}

.neumorphic-modal .modal-footer {
    background: var(--neu-bg-light);
    border-top: 1px solid var(--neu-border);
    border-radius: 0 0 var(--neu-radius) var(--neu-radius);
}

/* عناصر النموذج */
.neumorphic-input,
.neumorphic-select {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
    color: var(--neu-text);
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.neumorphic-input:focus,
.neumorphic-select:focus {
    outline: none;
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.neumorphic-checkbox {
    width: 20px;
    height: 20px;
    border-radius: var(--neu-radius-sm);
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-inset);
    border: none;
    cursor: pointer;
}

.neumorphic-checkbox:checked {
    background: var(--neu-primary);
    box-shadow: var(--neu-shadow);
}

/* أزرار Neumorphic */
.neumorphic-btn {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow);
    color: var(--neu-text);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.neumorphic-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
    color: var(--neu-text);
}

.neumorphic-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-inset);
}

.neumorphic-btn.btn-primary {
    background: var(--neu-primary);
    color: white;
}

.neumorphic-btn.btn-primary:hover {
    background: var(--neu-primary-dark);
    color: white;
}

/* حالات فارغة */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--neu-text-light);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 1.2rem;
}

/* التجاوب */
@media (max-width: 1024px) {
    .filter-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .inventory-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .inventory-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .inventory-container {
        padding: 0.5rem;
    }
    
    .inventory-header,
    .inventory-filters,
    .products-table-container {
        padding: 1rem;
    }
    
    .neumorphic-table thead th,
    .neumorphic-table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        min-width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .product-image-placeholder {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
}

/* تأثيرات التحميل */
.loading-row {
    background: linear-gradient(90deg, var(--neu-bg-light) 25%, var(--neu-bg) 50%, var(--neu-bg-light) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إضافية */
.form-label {
    color: var(--neu-text);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    border-radius: var(--neu-radius) 0 0 var(--neu-radius);
}

.input-group .btn {
    border-radius: 0 var(--neu-radius) var(--neu-radius) 0;
    border-left: 1px solid var(--neu-border);
}

.text-muted {
    color: var(--neu-text-light) !important;
}

.text-center {
    text-align: center;
}

.py-4 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}
