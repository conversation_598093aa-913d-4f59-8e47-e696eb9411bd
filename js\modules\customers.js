/**
 * وحدة إدارة العملاء
 * Customers Management Module
 */

export class Customers {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.customers = [];
        this.currentCustomer = null;
    }

    /**
     * عرض واجهة العملاء
     */
    async render() {
        try {
            await this.loadData();
            this.createCustomersInterface();
            this.bindEvents();
            this.loadCustomersList();
        } catch (error) {
            console.error('خطأ في عرض واجهة العملاء:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة العملاء');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        this.customers = this.storage.get('customers') || [];
    }

    /**
     * إنشاء واجهة العملاء
     */
    createCustomersInterface() {
        const container = document.getElementById('customers-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="customers-container">
                <div class="customers-header neumorphic-card">
                    <h2><i class="bi bi-people"></i> إدارة العملاء</h2>
                    <div class="customers-actions">
                        <button class="btn neumorphic-btn btn-primary" onclick="customersModule.showAddCustomerModal()">
                            <i class="bi bi-person-plus"></i>
                            إضافة عميل جديد
                        </button>
                        <button class="btn neumorphic-btn" onclick="customersModule.exportCustomers()">
                            <i class="bi bi-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="customers-stats neumorphic-card">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalCustomers">0</h3>
                                <p>إجمالي العملاء</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeCustomers">0</h3>
                                <p>العملاء النشطين</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalPurchases">0.00</h3>
                                <p>إجمالي المشتريات</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="bi bi-star-fill"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalLoyaltyPoints">0</h3>
                                <p>نقاط الولاء</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="customers-filters neumorphic-card">
                    <div class="filter-controls">
                        <div class="search-group">
                            <input type="text" id="customerSearchInput" class="form-control neumorphic-input" 
                                   placeholder="البحث بالاسم أو الهاتف أو البريد...">
                        </div>
                        <div class="filter-group">
                            <select id="statusFilterSelect" class="form-select neumorphic-select">
                                <option value="">جميع العملاء</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="customers-table-container neumorphic-card">
                    <div class="table-responsive">
                        <table class="table neumorphic-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>آخر شراء</th>
                                    <th>نقاط الولاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- Customers will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add/Edit Customer Modal -->
            <div class="modal fade" id="customerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content neumorphic-modal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="customerModalTitle">
                                <i class="bi bi-person-plus"></i>
                                إضافة عميل جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="customerForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل *</label>
                                    <input type="text" id="customerName" class="form-control neumorphic-input" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" id="customerPhone" class="form-control neumorphic-input" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" id="customerEmail" class="form-control neumorphic-input">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea id="customerAddress" class="form-control neumorphic-input" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نقاط الولاء</label>
                                    <input type="number" id="customerLoyaltyPoints" class="form-control neumorphic-input" 
                                           min="0" value="0">
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="customerActive" class="form-check-input neumorphic-checkbox" checked>
                                    <label class="form-check-label" for="customerActive">
                                        عميل نشط
                                    </label>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary neumorphic-btn" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-primary neumorphic-btn" onclick="customersModule.saveCustomer()">
                                <i class="bi bi-check-circle"></i>
                                حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Details Modal -->
            <div class="modal fade" id="customerDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content neumorphic-modal">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-person-circle"></i>
                                تفاصيل العميل
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="customerDetailsContent">
                            <!-- Customer details will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث
        const searchInput = document.getElementById('customerSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', this.filterCustomers.bind(this));
        }

        // تصفية الحالة
        const statusFilter = document.getElementById('statusFilterSelect');
        if (statusFilter) {
            statusFilter.addEventListener('change', this.filterCustomers.bind(this));
        }

        // ربط الوحدة بالنافذة
        window.customersModule = this;
    }

    /**
     * تحميل قائمة العملاء
     */
    loadCustomersList() {
        this.updateStats();
        this.filterCustomers();
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats() {
        const totalCustomers = this.customers.length;
        const activeCustomers = this.customers.filter(c => c.isActive).length;
        const totalPurchases = this.utils.sum(this.customers, 'totalPurchases');
        const totalLoyaltyPoints = this.utils.sum(this.customers, 'loyaltyPoints');

        document.getElementById('totalCustomers').textContent = totalCustomers;
        document.getElementById('activeCustomers').textContent = activeCustomers;
        document.getElementById('totalPurchases').textContent = this.utils.formatCurrency(totalPurchases);
        document.getElementById('totalLoyaltyPoints').textContent = totalLoyaltyPoints;
    }

    /**
     * تصفية العملاء
     */
    filterCustomers() {
        const searchTerm = document.getElementById('customerSearchInput')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilterSelect')?.value || '';

        let filteredCustomers = this.customers;

        // تصفية بالبحث
        if (searchTerm) {
            filteredCustomers = filteredCustomers.filter(customer => 
                this.utils.arabicSearch(customer.name, searchTerm) ||
                customer.phone.includes(searchTerm) ||
                (customer.email && customer.email.toLowerCase().includes(searchTerm))
            );
        }

        // تصفية بالحالة
        if (statusFilter) {
            filteredCustomers = filteredCustomers.filter(customer => {
                if (statusFilter === 'active') return customer.isActive;
                if (statusFilter === 'inactive') return !customer.isActive;
                return true;
            });
        }

        this.displayCustomers(filteredCustomers);
    }

    /**
     * عرض العملاء في الجدول
     */
    displayCustomers(customers) {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;

        if (customers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="bi bi-people" style="font-size: 2rem; color: var(--neu-text-light);"></i>
                        <p class="mt-2 mb-0">لا توجد عملاء</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = customers.map(customer => `
            <tr>
                <td>
                    <div class="customer-name">${customer.name}</div>
                    <small class="text-muted">ID: ${customer.id}</small>
                </td>
                <td>${this.utils.formatPhone(customer.phone)}</td>
                <td>${customer.email || '-'}</td>
                <td>${this.utils.formatCurrency(customer.totalPurchases || 0)}</td>
                <td>${customer.lastPurchase ? this.utils.formatDate(customer.lastPurchase, 'relative') : '-'}</td>
                <td>
                    <span class="badge neumorphic-badge badge-primary">
                        ${customer.loyaltyPoints || 0} نقطة
                    </span>
                </td>
                <td>
                    <span class="badge ${customer.isActive ? 'neumorphic-badge badge-success' : 'neumorphic-badge badge-secondary'}">
                        ${customer.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm neumorphic-btn" onclick="customersModule.viewCustomerDetails(${customer.id})" title="عرض التفاصيل">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm neumorphic-btn" onclick="customersModule.editCustomer(${customer.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm neumorphic-btn btn-danger" onclick="customersModule.deleteCustomer(${customer.id})" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * عرض نافذة إضافة عميل
     */
    showAddCustomerModal() {
        this.currentCustomer = null;
        document.getElementById('customerModalTitle').innerHTML = '<i class="bi bi-person-plus"></i> إضافة عميل جديد';
        this.resetCustomerForm();
        
        const modal = new bootstrap.Modal(document.getElementById('customerModal'));
        modal.show();
    }

    /**
     * تعديل عميل
     */
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        this.currentCustomer = customer;
        document.getElementById('customerModalTitle').innerHTML = '<i class="bi bi-pencil"></i> تعديل العميل';
        this.fillCustomerForm(customer);
        
        const modal = new bootstrap.Modal(document.getElementById('customerModal'));
        modal.show();
    }

    /**
     * عرض تفاصيل العميل
     */
    async viewCustomerDetails(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        // الحصول على مشتريات العميل
        const sales = this.storage.get('sales') || [];
        const customerSales = sales.filter(sale => sale.customerId === customerId);

        const content = `
            <div class="customer-details">
                <div class="row">
                    <div class="col-md-6">
                        <h6>المعلومات الأساسية</h6>
                        <table class="table table-borderless">
                            <tr><td><strong>الاسم:</strong></td><td>${customer.name}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${this.utils.formatPhone(customer.phone)}</td></tr>
                            <tr><td><strong>البريد:</strong></td><td>${customer.email || '-'}</td></tr>
                            <tr><td><strong>العنوان:</strong></td><td>${customer.address || '-'}</td></tr>
                            <tr><td><strong>نقاط الولاء:</strong></td><td>${customer.loyaltyPoints || 0}</td></tr>
                            <tr><td><strong>تاريخ التسجيل:</strong></td><td>${this.utils.formatDate(customer.createdAt)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>إحصائيات المشتريات</h6>
                        <table class="table table-borderless">
                            <tr><td><strong>عدد المشتريات:</strong></td><td>${customerSales.length}</td></tr>
                            <tr><td><strong>إجمالي المشتريات:</strong></td><td>${this.utils.formatCurrency(customer.totalPurchases || 0)}</td></tr>
                            <tr><td><strong>متوسط الشراء:</strong></td><td>${this.utils.formatCurrency(customerSales.length > 0 ? (customer.totalPurchases || 0) / customerSales.length : 0)}</td></tr>
                            <tr><td><strong>آخر شراء:</strong></td><td>${customer.lastPurchase ? this.utils.formatDate(customer.lastPurchase) : '-'}</td></tr>
                        </table>
                    </div>
                </div>
                
                ${customerSales.length > 0 ? `
                    <div class="mt-4">
                        <h6>آخر المشتريات</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${customerSales.slice(0, 5).map(sale => `
                                        <tr>
                                            <td>#${sale.number}</td>
                                            <td>${this.utils.formatDate(sale.createdAt)}</td>
                                            <td>${this.utils.formatCurrency(sale.total)}</td>
                                            <td>${this.getPaymentMethodName(sale.paymentMethod)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ` : '<div class="mt-4 text-center text-muted">لا توجد مشتريات</div>'}
            </div>
        `;

        document.getElementById('customerDetailsContent').innerHTML = content;
        
        const modal = new bootstrap.Modal(document.getElementById('customerDetailsModal'));
        modal.show();
    }

    /**
     * ملء نموذج العميل
     */
    fillCustomerForm(customer) {
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone;
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerLoyaltyPoints').value = customer.loyaltyPoints || 0;
        document.getElementById('customerActive').checked = customer.isActive;
    }

    /**
     * إعادة تعيين نموذج العميل
     */
    resetCustomerForm() {
        document.getElementById('customerForm').reset();
        document.getElementById('customerActive').checked = true;
        document.getElementById('customerLoyaltyPoints').value = 0;
    }

    /**
     * حفظ العميل
     */
    async saveCustomer() {
        try {
            const formData = this.getCustomerFormData();
            
            // التحقق من صحة البيانات
            if (!this.validateCustomerData(formData)) {
                return;
            }

            if (this.currentCustomer) {
                // تحديث عميل موجود
                await this.updateCustomer(formData);
            } else {
                // إضافة عميل جديد
                await this.addCustomer(formData);
            }

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('customerModal'));
            modal.hide();

            // تحديث القائمة
            this.loadCustomersList();

        } catch (error) {
            console.error('خطأ في حفظ العميل:', error);
            this.notifications.error('حدث خطأ في حفظ العميل');
        }
    }

    /**
     * الحصول على بيانات النموذج
     */
    getCustomerFormData() {
        return {
            name: document.getElementById('customerName').value.trim(),
            phone: document.getElementById('customerPhone').value.trim(),
            email: document.getElementById('customerEmail').value.trim(),
            address: document.getElementById('customerAddress').value.trim(),
            loyaltyPoints: parseInt(document.getElementById('customerLoyaltyPoints').value) || 0,
            isActive: document.getElementById('customerActive').checked
        };
    }

    /**
     * التحقق من صحة بيانات العميل
     */
    validateCustomerData(data) {
        if (!data.name) {
            this.notifications.error('اسم العميل مطلوب');
            return false;
        }

        if (!data.phone) {
            this.notifications.error('رقم الهاتف مطلوب');
            return false;
        }

        if (data.email && !this.utils.isValidEmail(data.email)) {
            this.notifications.error('البريد الإلكتروني غير صحيح');
            return false;
        }

        // فحص تكرار رقم الهاتف
        const existingCustomer = this.customers.find(c => 
            c.phone === data.phone && 
            (!this.currentCustomer || c.id !== this.currentCustomer.id)
        );
        
        if (existingCustomer) {
            this.notifications.error('رقم الهاتف موجود بالفعل');
            return false;
        }

        return true;
    }

    /**
     * إضافة عميل جديد
     */
    async addCustomer(data) {
        const newCustomer = {
            id: this.generateCustomerId(),
            ...data,
            totalPurchases: 0,
            lastPurchase: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.customers.push(newCustomer);
        this.storage.set('customers', this.customers);
        
        this.notifications.success('تم إضافة العميل بنجاح');
    }

    /**
     * تحديث عميل موجود
     */
    async updateCustomer(data) {
        const index = this.customers.findIndex(c => c.id === this.currentCustomer.id);
        if (index !== -1) {
            this.customers[index] = {
                ...this.currentCustomer,
                ...data,
                updatedAt: new Date().toISOString()
            };
            
            this.storage.set('customers', this.customers);
            this.notifications.success('تم تحديث العميل بنجاح');
        }
    }

    /**
     * حذف عميل
     */
    async deleteCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const confirmed = await this.notifications.confirm(
            `هل تريد حذف العميل "${customer.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const index = this.customers.findIndex(c => c.id === customerId);
            if (index !== -1) {
                this.customers.splice(index, 1);
                this.storage.set('customers', this.customers);
                this.loadCustomersList();
                this.notifications.success('تم حذف العميل بنجاح');
            }
        }
    }

    /**
     * توليد معرف عميل جديد
     */
    generateCustomerId() {
        const maxId = this.customers.reduce((max, customer) => Math.max(max, customer.id || 0), 0);
        return maxId + 1;
    }

    /**
     * تصدير العملاء
     */
    exportCustomers() {
        try {
            const data = {
                customers: this.customers,
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            this.utils.downloadFile(dataStr, `customers-${new Date().toISOString().split('T')[0]}.json`, 'application/json');
            
            this.notifications.success('تم تصدير العملاء بنجاح');
        } catch (error) {
            console.error('خطأ في تصدير العملاء:', error);
            this.notifications.error('حدث خطأ في تصدير العملاء');
        }
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    getPaymentMethodName(method) {
        const methods = {
            'cash': 'نقدي',
            'card': 'بطاقة ائتمان',
            'transfer': 'تحويل بنكي'
        };
        return methods[method] || method;
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadCustomersList();
    }
}
