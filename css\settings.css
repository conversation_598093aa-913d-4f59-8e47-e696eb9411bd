/**
 * تنسيقات الإعدادات
 * Settings Styles
 */

/* الحاوية الرئيسية */
.settings-container {
    padding: 1rem;
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.settings-header h2 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-actions {
    display: flex;
    gap: 1rem;
}

/* تبويبات الإعدادات */
.settings-tabs {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.nav-tabs {
    border-bottom: 2px solid var(--neu-border);
    margin-bottom: 2rem;
}

.nav-tabs .nav-item {
    margin-bottom: -2px;
}

.nav-tabs .nav-link {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
    box-shadow: var(--neu-shadow);
    color: var(--neu-text);
    padding: 1rem 1.5rem;
    margin-left: 0.5rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    background: var(--neu-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.nav-tabs .nav-link.active {
    background: var(--neu-primary);
    color: white;
    box-shadow: var(--neu-shadow-inset);
    transform: translateY(-2px);
}

/* محتوى التبويبات */
.tab-content {
    padding: 2rem;
    background: var(--neu-bg-light);
    border-radius: 0 var(--neu-radius) var(--neu-radius) var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
}

.tab-pane {
    min-height: 400px;
}

/* أقسام الإعدادات */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section h4 {
    margin: 0 0 1.5rem 0;
    color: var(--neu-primary);
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--neu-border);
}

/* مربعات الاختيار */
.settings-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    transition: all 0.3s ease;
}

.form-check:hover {
    background: var(--neu-bg-hover);
}

.form-check-input {
    width: 20px;
    height: 20px;
    border-radius: var(--neu-radius-sm);
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-inset);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background: var(--neu-primary);
    box-shadow: var(--neu-shadow);
}

.form-check-label {
    color: var(--neu-text);
    font-weight: 500;
    cursor: pointer;
    flex: 1;
}

/* قائمة المستخدمين */
.users-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow);
    transition: all 0.3s ease;
}

.user-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.user-info h6 {
    margin: 0 0 0.25rem 0;
    color: var(--neu-text);
    font-weight: 600;
}

.user-info p {
    margin: 0;
    color: var(--neu-text-light);
    font-size: 0.9rem;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

/* إجراءات النسخ الاحتياطية */
.backup-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.backup-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow);
    transition: all 0.3s ease;
}

.backup-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.backup-info h5 {
    margin: 0 0 0.5rem 0;
    color: var(--neu-text);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.backup-info p {
    margin: 0;
    color: var(--neu-text-light);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* معلومات التخزين */
.storage-info {
    padding: 1.5rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow);
}

.storage-info h5 {
    margin: 0 0 1rem 0;
    color: var(--neu-primary);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.storage-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--neu-border);
}

.storage-stat:last-child {
    border-bottom: none;
}

.storage-stat span:first-child {
    color: var(--neu-text);
}

.storage-stat span:last-child {
    font-weight: 600;
    color: var(--neu-primary);
}

/* عناصر النموذج المخصصة */
.neumorphic-input,
.neumorphic-select,
.neumorphic-textarea {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
    color: var(--neu-text);
    padding: 0.75rem;
    transition: all 0.3s ease;
    width: 100%;
}

.neumorphic-input:focus,
.neumorphic-select:focus,
.neumorphic-textarea:focus {
    outline: none;
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.neumorphic-textarea {
    resize: vertical;
    min-height: 100px;
}

/* تسميات النماذج */
.form-label {
    color: var(--neu-text);
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

/* أزرار الإعدادات */
.neumorphic-btn {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow);
    color: var(--neu-text);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.neumorphic-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
    color: var(--neu-text);
    text-decoration: none;
}

.neumorphic-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-inset);
}

.neumorphic-btn.btn-primary {
    background: var(--neu-primary);
    color: white;
}

.neumorphic-btn.btn-primary:hover {
    background: var(--neu-primary-dark);
    color: white;
}

.neumorphic-btn.btn-success {
    background: #27ae60;
    color: white;
}

.neumorphic-btn.btn-success:hover {
    background: #229954;
    color: white;
}

.neumorphic-btn.btn-warning {
    background: #f39c12;
    color: white;
}

.neumorphic-btn.btn-warning:hover {
    background: #e67e22;
    color: white;
}

.neumorphic-btn.btn-danger {
    background: #e74c3c;
    color: white;
}

.neumorphic-btn.btn-danger:hover {
    background: #c0392b;
    color: white;
}

.neumorphic-btn.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* التجاوب */
@media (max-width: 1024px) {
    .settings-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .settings-actions {
        justify-content: center;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-tabs .nav-link {
        margin: 0.25rem;
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 768px) {
    .settings-container {
        padding: 0.5rem;
    }
    
    .settings-header,
    .settings-tabs {
        padding: 1rem;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .backup-item {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        text-align: center;
    }
    
    .user-item {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .user-actions {
        justify-content: center;
    }
    
    .nav-tabs .nav-link {
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    .nav-tabs .nav-link i {
        font-size: 1.2rem;
    }
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إضافية */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0.75rem;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.mb-3 {
    margin-bottom: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}
