# 🚀 دليل البدء السريع - Quick Start Guide

## ⚡ التشغيل في 3 خطوات

### 1️⃣ تحميل المشروع
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/advanced-pos-system.git
cd advanced-pos-system

# أو تحميل ZIP وفك الضغط
```

### 2️⃣ تشغيل الخادم المحلي
اختر إحدى الطرق التالية:

#### 🐍 Python (الأسهل)
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### 📦 Node.js
```bash
# تثبيت serve عالمياً
npm install -g serve

# تشغيل الخادم
serve . -p 8000
```

#### 🔧 PHP
```bash
php -S localhost:8000
```

#### 🌐 Live Server (VS Code)
1. تثبيت إضافة Live Server
2. النقر بالزر الأيمن على `index.html`
3. اختيار "Open with Live Server"

### 3️⃣ فتح المتصفح
```
http://localhost:8000
```

## 🔑 تسجيل الدخول

### حسابات تجريبية جاهزة:

#### 👨‍💼 المدير (صلاحيات كاملة)
- **المستخدم**: `admin`
- **كلمة المرور**: `admin123`

#### 👨‍💻 الكاشير (صلاحيات محدودة)
- **المستخدم**: `cashier`
- **كلمة المرور**: `cashier123`

## 🎯 الخطوات الأولى

### 1. استكشاف لوحة التحكم
- عرض الإحصائيات اليومية
- مراجعة الرسوم البيانية
- فحص آخر المبيعات
- تنبيهات المخزون المنخفض

### 2. إضافة منتج تجريبي
1. انتقل لتبويب "المخزون"
2. اضغط "إضافة منتج جديد"
3. املأ البيانات:
   - **الاسم**: لابتوب HP
   - **الباركود**: 1234567890
   - **الفئة**: إلكترونيات
   - **سعر الشراء**: 2000
   - **سعر البيع**: 2500
   - **الكمية**: 10
4. احفظ المنتج

### 3. تجربة عملية بيع
1. انتقل لتبويب "المبيعات"
2. ابحث عن المنتج الذي أضفته
3. اضغط "إضافة" لإضافته للفاتورة
4. اختر طريقة الدفع
5. اضغط "إتمام البيع"

### 4. إضافة عميل جديد
1. انتقل لتبويب "العملاء"
2. اضغط "إضافة عميل جديد"
3. املأ البيانات:
   - **الاسم**: أحمد محمد
   - **الهاتف**: 0501234567
   - **البريد**: <EMAIL>
4. احفظ العميل

### 5. تجربة الواجهة اللمسية
1. انتقل لتبويب "شاشة لمس"
2. جرب النقر على المنتجات
3. استخدم سلة التسوق
4. أتمم عملية بيع

## 🎨 تخصيص النظام

### تغيير معلومات الشركة
1. انتقل لتبويب "الإعدادات" (للمدير فقط)
2. قسم "معلومات الشركة"
3. حدث البيانات:
   - اسم الشركة
   - العنوان
   - الهاتف
   - البريد الإلكتروني

### تخصيص إعدادات النظام
1. في تبويب "الإعدادات"
2. قسم "إعدادات النظام"
3. عدل:
   - العملة
   - معدل الضريبة
   - نص الفاتورة
   - إعدادات الطباعة

## 📱 تجربة الواجهات المختلفة

### 🖥️ الواجهة العادية
- مناسبة للاستخدام على الكمبيوتر
- جميع الميزات متاحة
- تفاصيل شاملة

### ⚡ الكاشير السريع
- واجهة مبسطة للعمليات السريعة
- أزرار منتجات سريعة
- اختصارات لوحة المفاتيح

### 📱 الشاشة اللمسية
- محسنة للأجهزة اللوحية
- أزرار كبيرة وسهلة اللمس
- تفاعل سلس

## 📊 استكشاف التقارير

### تقارير المبيعات
1. انتقل لتبويب "التقارير"
2. اختر "تقرير المبيعات"
3. حدد الفترة الزمنية
4. اعرض أو اطبع التقرير

### تحليلات متقدمة (للمدير)
1. انتقل لتبويب "التحليلات"
2. استكشف الرسوم البيانية
3. راجع أفضل المنتجات
4. تحليل اتجاهات المبيعات

## 💾 إدارة البيانات

### إنشاء نسخة احتياطية
1. انتقل لتبويب "الإعدادات"
2. قسم "النسخ الاحتياطية"
3. اضغط "تصدير البيانات"
4. احفظ الملف في مكان آمن

### استعادة البيانات
1. في نفس القسم
2. اضغط "استيراد البيانات"
3. اختر ملف النسخة الاحتياطية
4. تأكيد الاستعادة

## 🔧 حل المشاكل الشائعة

### ❌ النظام لا يعمل
**السبب**: لم يتم تشغيل خادم محلي
**الحل**: 
```bash
python -m http.server 8000
# ثم افتح http://localhost:8000
```

### ❌ البيانات لا تحفظ
**السبب**: المتصفح لا يدعم localStorage
**الحل**: 
- استخدم متصفح حديث (Chrome, Firefox, Safari)
- تأكد من عدم تفعيل الوضع الخاص

### ❌ الواجهة لا تظهر بشكل صحيح
**السبب**: مشكلة في تحميل ملفات CSS
**الحل**:
- تأكد من تشغيل خادم محلي
- فحص وحدة التحكم للأخطاء (F12)

### ❌ الخطوط العربية لا تظهر
**السبب**: عدم تحميل خطوط Google
**الحل**:
- تأكد من الاتصال بالإنترنت
- أو استخدم خطوط محلية

## 📚 الخطوات التالية

### للمستخدمين
1. 📖 اقرأ [دليل المستخدم الكامل](docs/user-guide.md)
2. 🎥 شاهد [فيديوهات تعليمية](docs/videos.md)
3. 💬 انضم لـ [مجتمع المستخدمين](https://github.com/your-repo/discussions)

### للمطورين
1. 🔧 اقرأ [دليل المطور](docs/developer-guide.md)
2. 🎨 تعلم [نظام التصميم](docs/design-system.md)
3. 🚀 ساهم في [تطوير المشروع](CONTRIBUTING.md)

## 🆘 الحصول على المساعدة

### 📞 الدعم السريع
- 🐛 [تقرير خطأ](https://github.com/your-repo/issues/new?template=bug_report.md)
- 💡 [طلب ميزة](https://github.com/your-repo/issues/new?template=feature_request.md)
- ❓ [سؤال عام](https://github.com/your-repo/discussions)

### 📧 التواصل المباشر
- **البريد الإلكتروني**: <EMAIL>
- **تليجرام**: @pos_support
- **واتساب**: +966-XX-XXX-XXXX

## ✅ قائمة فحص سريعة

- [ ] تم تحميل المشروع
- [ ] تم تشغيل الخادم المحلي
- [ ] تم فتح النظام في المتصفح
- [ ] تم تسجيل الدخول بنجاح
- [ ] تم إضافة منتج تجريبي
- [ ] تم إجراء عملية بيع تجريبية
- [ ] تم استكشاف الواجهات المختلفة
- [ ] تم إنشاء نسخة احتياطية

## 🎉 مبروك!

أنت الآن جاهز لاستخدام نظام نقطة البيع المتقدم! 

استمتع بالميزات المتطورة والتصميم الجميل. 🚀

---

**💡 نصيحة**: احفظ هذا الدليل في المفضلة للرجوع إليه لاحقاً.

**📅 آخر تحديث**: 2025-06-15  
**⏱️ وقت القراءة**: 10 دقائق
