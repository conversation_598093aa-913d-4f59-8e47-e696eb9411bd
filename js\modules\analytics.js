/**
 * وحدة التحليلات
 * Analytics Module
 */

export class Analytics {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
    }

    /**
     * عرض واجهة التحليلات
     */
    async render() {
        try {
            this.createAnalyticsInterface();
            this.bindEvents();
            this.loadAnalytics();
        } catch (error) {
            console.error('خطأ في عرض واجهة التحليلات:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة التحليلات');
        }
    }

    /**
     * إنشاء واجهة التحليلات
     */
    createAnalyticsInterface() {
        const container = document.getElementById('analytics-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="analytics-container">
                <div class="analytics-header neumorphic-card">
                    <h2><i class="bi bi-bar-chart"></i> التحليلات المتقدمة</h2>
                    <div class="analytics-filters">
                        <select id="analyticsTimeRange" class="form-select neumorphic-select">
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month" selected>هذا الشهر</option>
                            <option value="quarter">هذا الربع</option>
                            <option value="year">هذا العام</option>
                        </select>
                    </div>
                </div>

                <div class="analytics-overview">
                    <div class="overview-card neumorphic-card">
                        <div class="overview-icon">
                            <i class="bi bi-graph-up-arrow"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="totalRevenue">0.00 دج</h3>
                            <p>إجمالي الإيرادات</p>
                            <span class="trend positive" id="revenueTrend">+0%</span>
                        </div>
                    </div>

                    <div class="overview-card neumorphic-card">
                        <div class="overview-icon">
                            <i class="bi bi-cart-check"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="totalOrders">0</h3>
                            <p>إجمالي الطلبات</p>
                            <span class="trend positive" id="ordersTrend">+0%</span>
                        </div>
                    </div>

                    <div class="overview-card neumorphic-card">
                        <div class="overview-icon">
                            <i class="bi bi-cash-coin"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="averageOrder">0.00 دج</h3>
                            <p>متوسط قيمة الطلب</p>
                            <span class="trend neutral" id="avgOrderTrend">+0%</span>
                        </div>
                    </div>

                    <div class="overview-card neumorphic-card">
                        <div class="overview-icon">
                            <i class="bi bi-percent"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="profitMargin">0%</h3>
                            <p>هامش الربح</p>
                            <span class="trend positive" id="profitTrend">+0%</span>
                        </div>
                    </div>
                </div>

                <div class="analytics-charts">
                    <div class="chart-section neumorphic-card">
                        <h4><i class="bi bi-graph-up"></i> اتجاه المبيعات</h4>
                        <canvas id="salesTrendChart"></canvas>
                    </div>

                    <div class="chart-section neumorphic-card">
                        <h4><i class="bi bi-pie-chart"></i> توزيع المبيعات حسب الفئة</h4>
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>

                <div class="analytics-insights">
                    <div class="insights-section neumorphic-card">
                        <h4><i class="bi bi-lightbulb"></i> رؤى ذكية</h4>
                        <div id="smartInsights" class="insights-list">
                            <!-- Smart insights will be loaded here -->
                        </div>
                    </div>

                    <div class="top-products neumorphic-card">
                        <h4><i class="bi bi-star"></i> أفضل المنتجات</h4>
                        <div id="topProductsList" class="products-list">
                            <!-- Top products will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        const timeRangeSelect = document.getElementById('analyticsTimeRange');
        if (timeRangeSelect) {
            timeRangeSelect.addEventListener('change', this.loadAnalytics.bind(this));
        }

        window.analyticsModule = this;
    }

    /**
     * تحميل التحليلات
     */
    loadAnalytics() {
        const timeRange = document.getElementById('analyticsTimeRange')?.value || 'month';
        
        this.updateOverviewCards(timeRange);
        this.renderSalesTrendChart(timeRange);
        this.renderCategoryChart(timeRange);
        this.generateSmartInsights(timeRange);
        this.loadTopProducts(timeRange);
    }

    /**
     * تحديث بطاقات النظرة العامة
     */
    updateOverviewCards(timeRange) {
        const sales = this.storage.get('sales') || [];
        const filteredSales = this.filterSalesByTimeRange(sales, timeRange);
        
        const totalRevenue = this.utils.sum(filteredSales, 'total');
        const totalOrders = filteredSales.length;
        const averageOrder = totalOrders > 0 ? totalRevenue / totalOrders : 0;
        
        // حساب هامش الربح
        const products = this.storage.get('products') || [];
        let totalCost = 0;
        filteredSales.forEach(sale => {
            if (sale.items) {
                sale.items.forEach(item => {
                    const product = products.find(p => p.id === item.productId);
                    if (product) {
                        totalCost += product.buyingPrice * item.quantity;
                    }
                });
            }
        });
        
        const profitMargin = totalRevenue > 0 ? ((totalRevenue - totalCost) / totalRevenue) * 100 : 0;

        // تحديث العناصر
        document.getElementById('totalRevenue').textContent = this.utils.formatCurrency(totalRevenue);
        document.getElementById('totalOrders').textContent = totalOrders;
        document.getElementById('averageOrder').textContent = this.utils.formatCurrency(averageOrder);
        document.getElementById('profitMargin').textContent = `${profitMargin.toFixed(1)}%`;

        // حساب الاتجاهات (مقارنة مع الفترة السابقة)
        this.calculateTrends(timeRange, filteredSales);
    }

    /**
     * حساب الاتجاهات
     */
    calculateTrends(timeRange, currentSales) {
        // هذه دالة مبسطة - يمكن تطويرها أكثر
        const revenueTrend = Math.floor(Math.random() * 20) - 10; // رقم عشوائي للتوضيح
        const ordersTrend = Math.floor(Math.random() * 15) - 5;
        const avgOrderTrend = Math.floor(Math.random() * 10) - 5;
        const profitTrend = Math.floor(Math.random() * 8) - 4;

        this.updateTrendElement('revenueTrend', revenueTrend);
        this.updateTrendElement('ordersTrend', ordersTrend);
        this.updateTrendElement('avgOrderTrend', avgOrderTrend);
        this.updateTrendElement('profitTrend', profitTrend);
    }

    /**
     * تحديث عنصر الاتجاه
     */
    updateTrendElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const sign = value >= 0 ? '+' : '';
        element.textContent = `${sign}${value}%`;
        
        element.className = 'trend';
        if (value > 0) {
            element.classList.add('positive');
        } else if (value < 0) {
            element.classList.add('negative');
        } else {
            element.classList.add('neutral');
        }
    }

    /**
     * رسم مخطط اتجاه المبيعات
     */
    renderSalesTrendChart(timeRange) {
        const canvas = document.getElementById('salesTrendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية
        const data = this.generateSalesTrendData(timeRange);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'المبيعات',
                    data: data.values,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' دج';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * رسم مخطط الفئات
     */
    renderCategoryChart(timeRange) {
        const canvas = document.getElementById('categoryChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // بيانات تجريبية
        const data = {
            labels: ['إلكترونيات', 'ملابس', 'أغذية', 'أدوات منزلية'],
            values: [45, 25, 20, 10]
        };

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#3498db',
                        '#e74c3c',
                        '#f39c12',
                        '#27ae60'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    /**
     * توليد رؤى ذكية
     */
    generateSmartInsights(timeRange) {
        const container = document.getElementById('smartInsights');
        if (!container) return;

        const insights = [
            {
                icon: 'bi-graph-up',
                text: 'المبيعات في تزايد مستمر بنسبة 15% مقارنة بالشهر الماضي',
                type: 'positive'
            },
            {
                icon: 'bi-exclamation-triangle',
                text: 'انخفاض في مبيعات فئة الملابس بنسبة 8%',
                type: 'warning'
            },
            {
                icon: 'bi-star',
                text: 'منتج "لابتوب Dell" هو الأكثر مبيعاً هذا الشهر',
                type: 'info'
            },
            {
                icon: 'bi-clock',
                text: 'أفضل أوقات المبيعات: من 2 إلى 5 مساءً',
                type: 'info'
            }
        ];

        container.innerHTML = insights.map(insight => `
            <div class="insight-item ${insight.type}">
                <div class="insight-icon">
                    <i class="bi ${insight.icon}"></i>
                </div>
                <div class="insight-text">${insight.text}</div>
            </div>
        `).join('');
    }

    /**
     * تحميل أفضل المنتجات
     */
    loadTopProducts(timeRange) {
        const container = document.getElementById('topProductsList');
        if (!container) return;

        // بيانات تجريبية
        const topProducts = [
            { name: 'لابتوب Dell', sales: 25, revenue: 75000 },
            { name: 'ماوس لاسلكي', sales: 45, revenue: 3375 },
            { name: 'كيبورد ميكانيكي', sales: 18, revenue: 3600 },
            { name: 'شاشة Samsung', sales: 12, revenue: 18000 },
            { name: 'سماعات Bluetooth', sales: 30, revenue: 4500 }
        ];

        container.innerHTML = topProducts.map((product, index) => `
            <div class="product-item">
                <div class="product-rank">${index + 1}</div>
                <div class="product-info">
                    <div class="product-name">${product.name}</div>
                    <div class="product-stats">
                        ${product.sales} مبيعة • ${this.utils.formatCurrency(product.revenue)}
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * تصفية المبيعات حسب الفترة الزمنية
     */
    filterSalesByTimeRange(sales, timeRange) {
        const now = new Date();
        let startDate;

        switch (timeRange) {
            case 'today':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                break;
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                startDate = new Date(now.getFullYear(), quarter * 3, 1);
                break;
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                break;
            default:
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }

        return sales.filter(sale => new Date(sale.createdAt) >= startDate);
    }

    /**
     * توليد بيانات اتجاه المبيعات
     */
    generateSalesTrendData(timeRange) {
        // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية
        const labels = [];
        const values = [];

        switch (timeRange) {
            case 'today':
                for (let i = 0; i < 24; i++) {
                    labels.push(`${i}:00`);
                    values.push(Math.random() * 1000);
                }
                break;
            case 'week':
                const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                days.forEach(day => {
                    labels.push(day);
                    values.push(Math.random() * 5000);
                });
                break;
            case 'month':
                for (let i = 1; i <= 30; i++) {
                    labels.push(i.toString());
                    values.push(Math.random() * 3000);
                }
                break;
            default:
                for (let i = 1; i <= 12; i++) {
                    labels.push(`${i}/2024`);
                    values.push(Math.random() * 10000);
                }
        }

        return { labels, values };
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadAnalytics();
    }
}
