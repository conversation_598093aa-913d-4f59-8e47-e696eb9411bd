/**
 * وحدة الشاشة اللمسية
 * Touchscreen Module
 */

export class Touchscreen {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.products = [];
        this.categories = [];
        this.cart = [];
        this.currentCategory = '';
    }

    /**
     * عرض واجهة الشاشة اللمسية
     */
    async render() {
        try {
            await this.loadData();
            this.createTouchscreenInterface();
            this.bindEvents();
            this.loadProducts();
            this.updateCart();
        } catch (error) {
            console.error('خطأ في عرض الشاشة اللمسية:', error);
            this.notifications.error('حدث خطأ في تحميل الشاشة اللمسية');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        this.products = this.storage.get('products') || [];
        this.categories = [...new Set(this.products.map(p => p.category))];
        this.products = this.products.filter(p => p.isActive && p.stock > 0);
    }

    /**
     * إنشاء واجهة الشاشة اللمسية
     */
    createTouchscreenInterface() {
        const container = document.getElementById('touchscreen-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="touchscreen-container">
                <!-- Products Section -->
                <div class="products-section">
                    <div class="products-header">
                        <h3><i class="bi bi-grid-3x3-gap"></i> المنتجات</h3>
                        <div class="products-search">
                            <input type="text" id="touchProductSearch" class="form-control neumorphic-input" 
                                   placeholder="البحث عن منتج...">
                        </div>
                    </div>

                    <div class="category-filter">
                        <button class="category-btn ${!this.currentCategory ? 'active' : ''}" 
                                onclick="touchscreenModule.filterByCategory('')">
                            الكل
                        </button>
                        ${this.categories.map(category => `
                            <button class="category-btn ${this.currentCategory === category ? 'active' : ''}" 
                                    onclick="touchscreenModule.filterByCategory('${category}')">
                                ${category}
                            </button>
                        `).join('')}
                    </div>

                    <div class="products-grid" id="touchProductsGrid">
                        <!-- Products will be loaded here -->
                    </div>
                </div>

                <!-- Cart Section -->
                <div class="cart-section">
                    <div class="cart-header">
                        <h3><i class="bi bi-cart3"></i> سلة التسوق</h3>
                    </div>

                    <div class="cart-items" id="touchCartItems">
                        <!-- Cart items will be loaded here -->
                    </div>

                    <div class="cart-summary">
                        <div class="summary-row">
                            <span class="summary-label">المجموع الفرعي:</span>
                            <span class="summary-value" id="touchSubtotal">0.00 ر.س</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">الضريبة:</span>
                            <span class="summary-value" id="touchTax">0.00 ر.س</span>
                        </div>
                        <div class="summary-row total">
                            <span class="summary-label">المجموع الإجمالي:</span>
                            <span class="summary-value" id="touchTotal">0.00 ر.س</span>
                        </div>
                    </div>

                    <div class="cart-actions">
                        <button class="action-btn clear-btn" onclick="touchscreenModule.clearCart()">
                            <i class="bi bi-trash"></i>
                            مسح الكل
                        </button>
                        <button class="action-btn checkout-btn" onclick="touchscreenModule.checkout()" 
                                id="touchCheckoutBtn" disabled>
                            <i class="bi bi-check-circle"></i>
                            إتمام الشراء
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث
        const searchInput = document.getElementById('touchProductSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.searchProducts.bind(this));
        }

        // ربط الوحدة بالنافذة
        window.touchscreenModule = this;
    }

    /**
     * تحميل المنتجات
     */
    loadProducts() {
        this.filterProducts();
    }

    /**
     * تصفية المنتجات
     */
    filterProducts() {
        const searchTerm = document.getElementById('touchProductSearch')?.value.toLowerCase() || '';
        
        let filteredProducts = this.products;

        // تصفية بالفئة
        if (this.currentCategory) {
            filteredProducts = filteredProducts.filter(product => 
                product.category === this.currentCategory
            );
        }

        // تصفية بالبحث
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product => 
                this.utils.arabicSearch(product.name, searchTerm) ||
                product.barcode.toLowerCase().includes(searchTerm)
            );
        }

        this.displayProducts(filteredProducts);
    }

    /**
     * عرض المنتجات
     */
    displayProducts(products) {
        const container = document.getElementById('touchProductsGrid');
        if (!container) return;

        if (products.length === 0) {
            container.innerHTML = `
                <div class="empty-products">
                    <i class="bi bi-box"></i>
                    <p>لا توجد منتجات</p>
                </div>
            `;
            return;
        }

        container.innerHTML = products.map(product => `
            <div class="product-card" onclick="touchscreenModule.addToCart(${product.id})">
                <div class="product-image">
                    <i class="bi bi-box"></i>
                </div>
                <div class="product-name">${product.name}</div>
                <div class="product-price">${this.utils.formatCurrency(product.sellingPrice)}</div>
                <div class="product-stock ${product.stock <= product.minStock ? 'low' : ''}">
                    ${product.stock} ${product.unit}
                </div>
            </div>
        `).join('');
    }

    /**
     * البحث عن المنتجات
     */
    searchProducts() {
        this.filterProducts();
    }

    /**
     * تصفية حسب الفئة
     */
    filterByCategory(category) {
        this.currentCategory = category;
        
        // تحديث أزرار الفئات
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        event.target.classList.add('active');
        
        this.filterProducts();
    }

    /**
     * إضافة منتج للسلة
     */
    addToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // فحص المخزون
        const cartItem = this.cart.find(item => item.productId === productId);
        const currentQuantity = cartItem ? cartItem.quantity : 0;
        
        if (currentQuantity >= product.stock) {
            this.notifications.warning('لا يمكن إضافة كمية أكثر من المتوفر');
            return;
        }

        if (cartItem) {
            cartItem.quantity++;
            cartItem.total = cartItem.quantity * cartItem.price;
        } else {
            this.cart.push({
                productId: product.id,
                name: product.name,
                price: product.sellingPrice,
                quantity: 1,
                total: product.sellingPrice,
                unit: product.unit
            });
        }

        this.updateCart();
        this.utils.playSound('success');
    }

    /**
     * تحديث كمية منتج في السلة
     */
    updateQuantity(productId, change) {
        const cartItem = this.cart.find(item => item.productId === productId);
        if (!cartItem) return;

        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        const newQuantity = cartItem.quantity + change;

        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }

        if (newQuantity > product.stock) {
            this.notifications.warning('الكمية المطلوبة أكبر من المتوفر');
            return;
        }

        cartItem.quantity = newQuantity;
        cartItem.total = cartItem.quantity * cartItem.price;

        this.updateCart();
    }

    /**
     * حذف منتج من السلة
     */
    removeFromCart(productId) {
        const index = this.cart.findIndex(item => item.productId === productId);
        if (index !== -1) {
            this.cart.splice(index, 1);
            this.updateCart();
        }
    }

    /**
     * تحديث عرض السلة
     */
    updateCart() {
        this.updateCartItems();
        this.updateCartSummary();
    }

    /**
     * تحديث عناصر السلة
     */
    updateCartItems() {
        const container = document.getElementById('touchCartItems');
        if (!container) return;

        if (this.cart.length === 0) {
            container.innerHTML = `
                <div class="empty-cart">
                    <i class="bi bi-cart-x"></i>
                    <p>السلة فارغة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.cart.map(item => `
            <div class="cart-item">
                <div class="cart-item-image">
                    <i class="bi bi-box"></i>
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">${this.utils.formatCurrency(item.price)}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="touchscreenModule.updateQuantity(${item.productId}, -1)">
                        <i class="bi bi-dash"></i>
                    </button>
                    <span class="quantity-display">${item.quantity}</span>
                    <button class="quantity-btn" onclick="touchscreenModule.updateQuantity(${item.productId}, 1)">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button class="remove-btn" onclick="touchscreenModule.removeFromCart(${item.productId})">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث ملخص السلة
     */
    updateCartSummary() {
        const subtotal = this.utils.sum(this.cart, 'total');
        const taxRate = this.getTaxRate();
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        document.getElementById('touchSubtotal').textContent = this.utils.formatCurrency(subtotal);
        document.getElementById('touchTax').textContent = this.utils.formatCurrency(tax);
        document.getElementById('touchTotal').textContent = this.utils.formatCurrency(total);

        // تفعيل/تعطيل زر الدفع
        const checkoutBtn = document.getElementById('touchCheckoutBtn');
        if (checkoutBtn) {
            checkoutBtn.disabled = this.cart.length === 0;
        }
    }

    /**
     * مسح السلة
     */
    clearCart() {
        if (this.cart.length === 0) return;

        this.cart = [];
        this.updateCart();
        this.notifications.info('تم مسح السلة');
    }

    /**
     * إتمام الشراء
     */
    async checkout() {
        if (this.cart.length === 0) {
            this.notifications.warning('السلة فارغة');
            return;
        }

        try {
            const subtotal = this.utils.sum(this.cart, 'total');
            const taxRate = this.getTaxRate();
            const tax = subtotal * taxRate;
            const total = subtotal + tax;

            // تأكيد الشراء
            const confirmed = await this.notifications.confirm(
                `إجمالي المبلغ: ${this.utils.formatCurrency(total)}\nهل تريد إتمام الشراء؟`,
                'تأكيد الشراء'
            );

            if (!confirmed) return;

            // إنشاء الفاتورة
            const sale = {
                id: this.generateSaleId(),
                number: this.generateSaleNumber(),
                items: [...this.cart],
                subtotal: subtotal,
                discount: 0,
                tax: tax,
                total: total,
                customerId: null,
                customerName: 'عميل نقدي',
                paymentMethod: 'cash',
                cashierId: this.app.getCurrentUser().id,
                cashierName: this.app.getCurrentUser().fullName,
                createdAt: new Date().toISOString(),
                status: 'completed'
            };

            // حفظ الفاتورة
            const sales = this.storage.get('sales') || [];
            sales.unshift(sale);
            this.storage.set('sales', sales);

            // تحديث المخزون
            await this.updateInventory(this.cart);

            // مسح السلة
            this.clearCart();

            this.notifications.success('تم إتمام الشراء بنجاح');
            this.utils.playSound('success');

            // إعادة تحميل المنتجات
            await this.loadData();
            this.loadProducts();

        } catch (error) {
            console.error('خطأ في إتمام الشراء:', error);
            this.notifications.error('حدث خطأ في إتمام الشراء');
        }
    }

    /**
     * تحديث المخزون
     */
    async updateInventory(items) {
        const products = this.storage.get('products') || [];
        
        items.forEach(item => {
            const productIndex = products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                products[productIndex].stock -= item.quantity;
            }
        });

        this.storage.set('products', products);
    }

    /**
     * توليد رقم فاتورة
     */
    generateSaleNumber() {
        const today = new Date();
        const dateStr = today.toISOString().split('T')[0].replace(/-/g, '');
        const timeStr = today.getTime().toString().slice(-4);
        return `${dateStr}${timeStr}`;
    }

    /**
     * توليد معرف فاتورة
     */
    generateSaleId() {
        const sales = this.storage.get('sales') || [];
        const maxId = sales.reduce((max, sale) => Math.max(max, sale.id || 0), 0);
        return maxId + 1;
    }

    /**
     * الحصول على معدل الضريبة
     */
    getTaxRate() {
        const settings = this.storage.get('settings') || {};
        return settings.pos?.taxRate || 0.15;
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadProducts();
        this.clearCart();
    }
}
