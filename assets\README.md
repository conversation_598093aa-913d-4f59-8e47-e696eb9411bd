# مجلد الأصول - Assets Folder

هذا المجلد يحتوي على الأصول المطلوبة لتطبيق سطح المكتب:

## الأيقونات المطلوبة:

### Windows
- `icon.ico` - أيقونة Windows (256x256, 128x128, 64x64, 48x48, 32x32, 16x16)

### macOS
- `icon.icns` - أيقونة macOS (1024x1024, 512x512, 256x256, 128x128, 64x64, 32x32, 16x16)

### Linux
- `icon.png` - أيقونة Linux (512x512 PNG)

## إنشاء الأيقونات:

يمكنك استخدام الأدوات التالية لإنشاء الأيقونات:

### أدوات مجانية:
1. **GIMP** - برنامج تحرير صور مجاني
2. **Inkscape** - برنامج رسوم متجهة مجاني
3. **IcoFX** - محرر أيقونات مجاني

### أدوات أونلاين:
1. **Favicon.io** - https://favicon.io/
2. **ConvertICO** - https://convertico.com/
3. **ICO Convert** - https://icoconvert.com/

### أدوات سطر الأوامر:
```bash
# تحويل PNG إلى ICO
convert icon.png -resize 256x256 icon.ico

# تحويل PNG إلى ICNS (macOS)
png2icns icon.icns icon.png
```

## مواصفات الأيقونة المقترحة:

- **الموضوع**: رمز متجر أو نقطة بيع
- **الألوان**: متدرج أزرق/بنفسجي ليتماشى مع التصميم
- **الشكل**: دائري أو مربع مع زوايا مدورة
- **العناصر**: 
  - رمز متجر (🏪)
  - أو رمز نقطة بيع
  - أو رمز كاشير
  - خلفية متدرجة

## ملاحظات:

- تأكد من أن الأيقونة واضحة في جميع الأحجام
- استخدم ألوان متباينة للوضوح
- تجنب التفاصيل الدقيقة التي قد تختفي في الأحجام الصغيرة
- اختبر الأيقونة على خلفيات مختلفة

## إنشاء أيقونة مؤقتة:

إذا لم تكن لديك أيقونة مخصصة، يمكنك استخدام أيقونة افتراضية مؤقتة:

```bash
# إنشاء أيقونة PNG بسيطة
convert -size 512x512 xc:transparent -fill "#667eea" -draw "circle 256,256 256,100" -fill white -pointsize 200 -gravity center -annotate +0+0 "🏪" icon.png

# تحويل إلى تنسيقات أخرى
convert icon.png icon.ico
png2icns icon.icns icon.png
```
