<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقطة البيع المتقدم - Advanced POS System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/neumorphic.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/sales.css">
    <link rel="stylesheet" href="css/inventory.css">
    <link rel="stylesheet" href="css/customers.css">
    <link rel="stylesheet" href="css/reports.css">
    <link rel="stylesheet" href="css/settings.css">
    <link rel="stylesheet" href="css/touchscreen.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-logo">
                <i class="bi bi-shop"></i>
            </div>
            <div class="loading-text">نظام نقطة البيع المتقدم</div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="loginScreen" class="login-screen d-none">
        <div class="login-container">
            <div class="login-card neumorphic-card">
                <div class="login-header">
                    <div class="login-logo">
                        <i class="bi bi-shop"></i>
                    </div>
                    <h2 class="login-title">نظام نقطة البيع المتقدم</h2>
                    <p class="login-subtitle">Advanced POS System</p>
                </div>
                
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="bi bi-person"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="username" class="form-control neumorphic-input" 
                               placeholder="أدخل اسم المستخدم" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="bi bi-lock"></i>
                            كلمة المرور
                        </label>
                        <input type="password" id="password" class="form-control neumorphic-input" 
                               placeholder="أدخل كلمة المرور" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary neumorphic-btn login-btn">
                        <i class="bi bi-box-arrow-in-right"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="login-demo">
                    <h6>حسابات تجريبية:</h6>
                    <div class="demo-accounts">
                        <button class="btn btn-outline-secondary btn-sm demo-btn" 
                                onclick="fillLogin('admin', 'admin123')">
                            <i class="bi bi-person-gear"></i>
                            مدير
                        </button>
                        <button class="btn btn-outline-secondary btn-sm demo-btn" 
                                onclick="fillLogin('cashier', 'cashier123')">
                            <i class="bi bi-cash-coin"></i>
                            كاشير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="main-app d-none">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg neumorphic-navbar">
            <div class="container-fluid">
                <div class="navbar-brand">
                    <i class="bi bi-shop"></i>
                    <span class="brand-text">نقطة البيع المتقدم</span>
                </div>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-menu" href="#" role="button" 
                           data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span id="currentUser">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end neumorphic-dropdown">
                            <li><a class="dropdown-item" href="#" onclick="showProfile()">
                                <i class="bi bi-person"></i> الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSettings()">
                                <i class="bi bi-gear"></i> الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar neumorphic-sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item active" data-tab="dashboard">
                        <i class="bi bi-speedometer2"></i>
                        <span>لوحة التحكم</span>
                    </div>
                    <div class="menu-item" data-tab="sales">
                        <i class="bi bi-cart3"></i>
                        <span>المبيعات</span>
                    </div>
                    <div class="menu-item" data-tab="quick-cashier">
                        <i class="bi bi-lightning"></i>
                        <span>كاشير سريع</span>
                    </div>
                    <div class="menu-item" data-tab="touchscreen">
                        <i class="bi bi-tablet"></i>
                        <span>شاشة لمس</span>
                    </div>
                    <div class="menu-item" data-tab="inventory">
                        <i class="bi bi-boxes"></i>
                        <span>المخزون</span>
                    </div>
                    <div class="menu-item" data-tab="customers">
                        <i class="bi bi-people"></i>
                        <span>العملاء</span>
                    </div>
                    <div class="menu-item" data-tab="suppliers" data-role="admin,manager">
                        <i class="bi bi-building"></i>
                        <span>الموردين</span>
                    </div>
                    <div class="menu-item" data-tab="promotions" data-role="admin,manager">
                        <i class="bi bi-tags"></i>
                        <span>العروض</span>
                    </div>
                    <div class="menu-item" data-tab="reports">
                        <i class="bi bi-graph-up"></i>
                        <span>التقارير</span>
                    </div>
                    <div class="menu-item" data-tab="analytics" data-role="admin,manager">
                        <i class="bi bi-bar-chart"></i>
                        <span>التحليلات</span>
                    </div>
                    <div class="menu-item" data-tab="settings" data-role="admin">
                        <i class="bi bi-gear"></i>
                        <span>الإعدادات</span>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Dashboard Tab -->
                <div id="dashboard-tab" class="tab-content active">
                    <div class="dashboard-header">
                        <h2><i class="bi bi-speedometer2"></i> لوحة التحكم</h2>
                        <div class="dashboard-date">
                            <i class="bi bi-calendar3"></i>
                            <span id="currentDate"></span>
                        </div>
                    </div>
                    
                    <div class="dashboard-stats">
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todaySales">0.00</h3>
                                <p>مبيعات اليوم</p>
                            </div>
                        </div>
                        
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayOrders">0</h3>
                                <p>فواتير اليوم</p>
                            </div>
                        </div>
                        
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalCustomers">0</h3>
                                <p>إجمالي العملاء</p>
                            </div>
                        </div>
                        
                        <div class="stat-card neumorphic-card">
                            <div class="stat-icon">
                                <i class="bi bi-boxes"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalProducts">0</h3>
                                <p>إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-charts">
                        <div class="chart-container neumorphic-card">
                            <h4><i class="bi bi-graph-up"></i> مبيعات الأسبوع</h4>
                            <canvas id="weeklyChart"></canvas>
                        </div>
                        
                        <div class="chart-container neumorphic-card">
                            <h4><i class="bi bi-pie-chart"></i> أفضل المنتجات</h4>
                            <canvas id="productsChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="dashboard-recent">
                        <div class="recent-sales neumorphic-card">
                            <h4><i class="bi bi-clock-history"></i> آخر المبيعات</h4>
                            <div id="recentSalesList" class="recent-list">
                                <!-- Recent sales will be populated here -->
                            </div>
                        </div>
                        
                        <div class="low-stock neumorphic-card">
                            <h4><i class="bi bi-exclamation-triangle"></i> منتجات نفدت</h4>
                            <div id="lowStockList" class="stock-list">
                                <!-- Low stock items will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other tabs will be loaded dynamically -->
                <div id="sales-tab" class="tab-content d-none"></div>
                <div id="quick-cashier-tab" class="tab-content d-none"></div>
                <div id="touchscreen-tab" class="tab-content d-none"></div>
                <div id="inventory-tab" class="tab-content d-none"></div>
                <div id="customers-tab" class="tab-content d-none"></div>
                <div id="suppliers-tab" class="tab-content d-none"></div>
                <div id="promotions-tab" class="tab-content d-none"></div>
                <div id="reports-tab" class="tab-content d-none"></div>
                <div id="analytics-tab" class="tab-content d-none"></div>
                <div id="settings-tab" class="tab-content d-none"></div>
            </div>
        </div>
    </div>

    <!-- Modals and Overlays -->
    <div id="modalContainer"></div>
    
    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Configuration -->
    <script src="config.js"></script>
    
    <!-- Core JavaScript Modules -->
    <script type="module" src="js/core/app.js"></script>
    <script type="module" src="js/core/auth.js"></script>
    <script type="module" src="js/core/storage.js"></script>
    <script type="module" src="js/core/utils.js"></script>
    <script type="module" src="js/core/notifications.js"></script>
    
    <!-- Feature Modules -->
    <script type="module" src="js/modules/dashboard.js"></script>
    <script type="module" src="js/modules/sales.js"></script>
    <script type="module" src="js/modules/inventory.js"></script>
    <script type="module" src="js/modules/customers.js"></script>
    <script type="module" src="js/modules/reports.js"></script>
    <script type="module" src="js/modules/settings.js"></script>
    <script type="module" src="js/modules/touchscreen.js"></script>
    <script type="module" src="js/modules/quickCashier.js"></script>
    <script type="module" src="js/modules/suppliers.js"></script>
    <script type="module" src="js/modules/promotions.js"></script>
    <script type="module" src="js/modules/analytics.js"></script>
    
    <!-- Initialize App -->
    <script type="module">
        import { App } from './js/core/app.js';
        
        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            const app = new App();
            app.init();
        });
    </script>
</body>
</html>
