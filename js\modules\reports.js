/**
 * وحدة التقارير
 * Reports Module
 */

export class Reports {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
    }

    /**
     * عرض واجهة التقارير
     */
    async render() {
        try {
            this.createReportsInterface();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في عرض واجهة التقارير:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة التقارير');
        }
    }

    /**
     * إنشاء واجهة التقارير
     */
    createReportsInterface() {
        const container = document.getElementById('reports-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="reports-container">
                <div class="reports-header neumorphic-card">
                    <h2><i class="bi bi-graph-up"></i> التقارير</h2>
                </div>

                <div class="reports-grid">
                    <div class="report-card neumorphic-card" onclick="reportsModule.generateSalesReport()">
                        <div class="report-icon">
                            <i class="bi bi-cart3"></i>
                        </div>
                        <div class="report-info">
                            <h4>تقرير المبيعات</h4>
                            <p>تقرير شامل عن المبيعات والأرباح</p>
                        </div>
                    </div>

                    <div class="report-card neumorphic-card" onclick="reportsModule.generateInventoryReport()">
                        <div class="report-icon">
                            <i class="bi bi-boxes"></i>
                        </div>
                        <div class="report-info">
                            <h4>تقرير المخزون</h4>
                            <p>حالة المخزون والمنتجات</p>
                        </div>
                    </div>

                    <div class="report-card neumorphic-card" onclick="reportsModule.generateCustomersReport()">
                        <div class="report-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="report-info">
                            <h4>تقرير العملاء</h4>
                            <p>إحصائيات وبيانات العملاء</p>
                        </div>
                    </div>

                    <div class="report-card neumorphic-card" onclick="reportsModule.generateFinancialReport()">
                        <div class="report-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="report-info">
                            <h4>التقرير المالي</h4>
                            <p>الإيرادات والمصروفات</p>
                        </div>
                    </div>
                </div>

                <div id="reportContent" class="report-content neumorphic-card d-none">
                    <!-- Report content will be displayed here -->
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        window.reportsModule = this;
    }

    /**
     * تقرير المبيعات
     */
    generateSalesReport() {
        const sales = this.storage.get('sales') || [];
        const today = new Date();
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        const todaySales = sales.filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate.toDateString() === today.toDateString();
        });

        const monthSales = sales.filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate >= thisMonth;
        });

        const content = `
            <div class="report-header">
                <h3><i class="bi bi-cart3"></i> تقرير المبيعات</h3>
                <div class="report-actions">
                    <button class="btn neumorphic-btn" onclick="reportsModule.printReport()">
                        <i class="bi bi-printer"></i> طباعة
                    </button>
                    <button class="btn neumorphic-btn" onclick="reportsModule.exportReport()">
                        <i class="bi bi-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="report-stats">
                <div class="stat-item">
                    <h4>${this.utils.formatCurrency(this.utils.sum(todaySales, 'total'))}</h4>
                    <p>مبيعات اليوم</p>
                </div>
                <div class="stat-item">
                    <h4>${this.utils.formatCurrency(this.utils.sum(monthSales, 'total'))}</h4>
                    <p>مبيعات الشهر</p>
                </div>
                <div class="stat-item">
                    <h4>${todaySales.length}</h4>
                    <p>فواتير اليوم</p>
                </div>
                <div class="stat-item">
                    <h4>${monthSales.length}</h4>
                    <p>فواتير الشهر</p>
                </div>
            </div>

            <div class="report-table">
                <h5>تفاصيل المبيعات</h5>
                <table class="table neumorphic-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${sales.slice(0, 20).map(sale => `
                            <tr>
                                <td>#${sale.number}</td>
                                <td>${this.utils.formatDate(sale.createdAt)}</td>
                                <td>${sale.customerName}</td>
                                <td>${this.utils.formatCurrency(sale.total)}</td>
                                <td>${this.getPaymentMethodName(sale.paymentMethod)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport(content);
    }

    /**
     * تقرير المخزون
     */
    generateInventoryReport() {
        const products = this.storage.get('products') || [];
        const lowStockProducts = products.filter(p => p.stock <= p.minStock);
        const outOfStockProducts = products.filter(p => p.stock <= 0);

        const content = `
            <div class="report-header">
                <h3><i class="bi bi-boxes"></i> تقرير المخزون</h3>
                <div class="report-actions">
                    <button class="btn neumorphic-btn" onclick="reportsModule.printReport()">
                        <i class="bi bi-printer"></i> طباعة
                    </button>
                </div>
            </div>

            <div class="report-stats">
                <div class="stat-item">
                    <h4>${products.length}</h4>
                    <p>إجمالي المنتجات</p>
                </div>
                <div class="stat-item">
                    <h4>${lowStockProducts.length}</h4>
                    <p>مخزون منخفض</p>
                </div>
                <div class="stat-item">
                    <h4>${outOfStockProducts.length}</h4>
                    <p>نفد المخزون</p>
                </div>
                <div class="stat-item">
                    <h4>${this.utils.formatCurrency(this.utils.sum(products.map(p => ({ value: p.stock * p.buyingPrice })), 'value'))}</h4>
                    <p>قيمة المخزون</p>
                </div>
            </div>

            <div class="report-table">
                <h5>المنتجات منخفضة المخزون</h5>
                <table class="table neumorphic-table">
                    <thead>
                        <tr>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${lowStockProducts.map(product => `
                            <tr>
                                <td>${product.name}</td>
                                <td>${product.category}</td>
                                <td>${product.stock} ${product.unit}</td>
                                <td>${product.minStock} ${product.unit}</td>
                                <td>
                                    <span class="badge ${product.stock <= 0 ? 'badge-danger' : 'badge-warning'}">
                                        ${product.stock <= 0 ? 'نفد' : 'منخفض'}
                                    </span>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport(content);
    }

    /**
     * تقرير العملاء
     */
    generateCustomersReport() {
        const customers = this.storage.get('customers') || [];
        const activeCustomers = customers.filter(c => c.isActive);
        const topCustomers = customers
            .sort((a, b) => (b.totalPurchases || 0) - (a.totalPurchases || 0))
            .slice(0, 10);

        const content = `
            <div class="report-header">
                <h3><i class="bi bi-people"></i> تقرير العملاء</h3>
                <div class="report-actions">
                    <button class="btn neumorphic-btn" onclick="reportsModule.printReport()">
                        <i class="bi bi-printer"></i> طباعة
                    </button>
                </div>
            </div>

            <div class="report-stats">
                <div class="stat-item">
                    <h4>${customers.length}</h4>
                    <p>إجمالي العملاء</p>
                </div>
                <div class="stat-item">
                    <h4>${activeCustomers.length}</h4>
                    <p>العملاء النشطين</p>
                </div>
                <div class="stat-item">
                    <h4>${this.utils.formatCurrency(this.utils.sum(customers, 'totalPurchases'))}</h4>
                    <p>إجمالي المشتريات</p>
                </div>
                <div class="stat-item">
                    <h4>${this.utils.sum(customers, 'loyaltyPoints')}</h4>
                    <p>نقاط الولاء</p>
                </div>
            </div>

            <div class="report-table">
                <h5>أفضل العملاء</h5>
                <table class="table neumorphic-table">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>إجمالي المشتريات</th>
                            <th>نقاط الولاء</th>
                            <th>آخر شراء</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${topCustomers.map(customer => `
                            <tr>
                                <td>${customer.name}</td>
                                <td>${this.utils.formatPhone(customer.phone)}</td>
                                <td>${this.utils.formatCurrency(customer.totalPurchases || 0)}</td>
                                <td>${customer.loyaltyPoints || 0}</td>
                                <td>${customer.lastPurchase ? this.utils.formatDate(customer.lastPurchase) : '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport(content);
    }

    /**
     * التقرير المالي
     */
    generateFinancialReport() {
        const sales = this.storage.get('sales') || [];
        const products = this.storage.get('products') || [];

        const totalRevenue = this.utils.sum(sales, 'total');
        const totalTax = this.utils.sum(sales, 'tax');
        const totalDiscount = this.utils.sum(sales, 'discount');

        // حساب تكلفة البضاعة المباعة
        let totalCost = 0;
        sales.forEach(sale => {
            if (sale.items) {
                sale.items.forEach(item => {
                    const product = products.find(p => p.id === item.productId);
                    if (product) {
                        totalCost += product.buyingPrice * item.quantity;
                    }
                });
            }
        });

        const grossProfit = totalRevenue - totalCost;
        const netProfit = grossProfit - totalTax;

        const content = `
            <div class="report-header">
                <h3><i class="bi bi-currency-dollar"></i> التقرير المالي</h3>
                <div class="report-actions">
                    <button class="btn neumorphic-btn" onclick="reportsModule.printReport()">
                        <i class="bi bi-printer"></i> طباعة
                    </button>
                </div>
            </div>

            <div class="financial-summary">
                <div class="summary-section">
                    <h5>الإيرادات</h5>
                    <div class="financial-item">
                        <span>إجمالي المبيعات:</span>
                        <span>${this.utils.formatCurrency(totalRevenue)}</span>
                    </div>
                    <div class="financial-item">
                        <span>الخصومات:</span>
                        <span class="text-danger">-${this.utils.formatCurrency(totalDiscount)}</span>
                    </div>
                    <div class="financial-item">
                        <span>الضرائب:</span>
                        <span>${this.utils.formatCurrency(totalTax)}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h5>التكاليف والأرباح</h5>
                    <div class="financial-item">
                        <span>تكلفة البضاعة:</span>
                        <span class="text-danger">-${this.utils.formatCurrency(totalCost)}</span>
                    </div>
                    <div class="financial-item">
                        <span>الربح الإجمالي:</span>
                        <span class="text-success">${this.utils.formatCurrency(grossProfit)}</span>
                    </div>
                    <div class="financial-item">
                        <span>الربح الصافي:</span>
                        <span class="text-success">${this.utils.formatCurrency(netProfit)}</span>
                    </div>
                </div>
            </div>

            <div class="profit-margin">
                <h5>هوامش الربح</h5>
                <div class="margin-item">
                    <span>هامش الربح الإجمالي:</span>
                    <span>${totalRevenue > 0 ? ((grossProfit / totalRevenue) * 100).toFixed(2) : 0}%</span>
                </div>
                <div class="margin-item">
                    <span>هامش الربح الصافي:</span>
                    <span>${totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(2) : 0}%</span>
                </div>
            </div>
        `;

        this.showReport(content);
    }

    /**
     * عرض التقرير
     */
    showReport(content) {
        const reportContainer = document.getElementById('reportContent');
        if (reportContainer) {
            reportContainer.innerHTML = content;
            reportContainer.classList.remove('d-none');

            // التمرير للتقرير
            reportContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }

    /**
     * طباعة التقرير
     */
    printReport() {
        const reportContent = document.getElementById('reportContent');
        if (reportContent) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>تقرير</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .report-header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
                        .report-stats { display: flex; justify-content: space-around; margin: 20px 0; }
                        .stat-item { text-align: center; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f5f5f5; }
                        @media print { .report-actions { display: none; } }
                    </style>
                </head>
                <body>
                    ${reportContent.innerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    }

    /**
     * تصدير التقرير
     */
    exportReport() {
        const reportContent = document.getElementById('reportContent');
        if (reportContent) {
            const content = reportContent.innerHTML;
            this.utils.downloadFile(content, `report-${new Date().toISOString().split('T')[0]}.html`, 'text/html');
            this.notifications.success('تم تصدير التقرير بنجاح');
        }
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    getPaymentMethodName(method) {
        const methods = {
            'cash': 'نقدي',
            'card': 'بطاقة ائتمان',
            'transfer': 'تحويل بنكي'
        };
        return methods[method] || method;
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        // لا حاجة لإعادة تحميل خاصة
    }
}