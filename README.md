# 🏪 نظام نقطة البيع المتقدم - Advanced POS System

نظام نقطة بيع متطور مبني بتقنيات الويب الحديثة مع تصميم Neumorphic وواجهة عربية RTL.

## ✨ المميزات الرئيسية

### 🎨 التصميم والواجهة
- **تصميم Neumorphic** عصري وجذاب
- **واجهة عربية RTL** كاملة
- **متجاوب** مع جميع أحجام الشاشات
- **أيقونات Bootstrap** احترافية
- **رسوم متحركة** سلسة ومتطورة

### 💾 التخزين والبيانات
- **تخزين محلي** بدون الحاجة للإنترنت
- **localStorage** مع ضغط وتشفير البيانات
- **نسخ احتياطية** تلقائية ويدوية
- **استيراد وتصدير** البيانات

### 🔐 الأمان والمصادقة
- **نظام مستخدمين** متعدد المستويات
- **صلاحيات مخصصة** لكل مستخدم
- **تشفير كلمات المرور** بـ SHA-256
- **جلسات آمنة** مع انتهاء صلاحية

### 📱 واجهات متعددة
- **لوحة التحكم** مع إحصائيات مرئية
- **المبيعات العادية** للاستخدام التقليدي
- **كاشير سريع** للعمليات السريعة
- **شاشة لمسية** محسنة للأجهزة اللوحية
- **إدارة المخزون** شاملة
- **إدارة العملاء** والموردين
- **التقارير والتحليلات** المتقدمة

## 🚀 التشغيل السريع

### 🖥️ تطبيق سطح المكتب (الموصى به)

#### التشغيل المباشر
```bash
# تحميل المشروع
git clone https://github.com/your-repo/web-pos.git
cd web-pos

# تثبيت التبعيات
npm install

# تشغيل تطبيق سطح المكتب
npm run electron
```

#### بناء التطبيق للتوزيع
```bash
# بناء لنظام Windows (32-bit و 64-bit)
npm run build-win

# بناء لنظام macOS
npm run build-mac

# بناء لنظام Linux
npm run build-linux

# بناء لجميع المنصات
npm run build-all
```

**الملفات المُنتجة**: ستجد التطبيق في مجلد `dist/`
- `Advanced POS Setup 1.0.0.exe` - مثبت Windows
- `AdvancedPOS-Portable-1.0.0-x64.exe` - نسخة محمولة 64-bit
- `AdvancedPOS-Portable-1.0.0-ia32.exe` - نسخة محمولة 32-bit

### 🌐 تطبيق ويب

#### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/web-pos.git
cd web-pos
```

#### 2. تشغيل الخادم المحلي
```bash
# باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx serve .

# أو باستخدام PHP
php -S localhost:8000
```

#### 3. فتح المتصفح
```
http://localhost:8000
```

### 🔑 تسجيل الدخول
- **المدير**: `admin` / `admin123` (جميع الصلاحيات)
- **الكاشير**: `cashier` / `cashier123` (المبيعات والعملاء فقط)

### 💰 العملة
- **العملة الافتراضية**: الدينار الجزائري (دج)
- **معدل الضريبة**: 19% (ضريبة الجزائر)
- **يمكن تخصيص العملة** من الإعدادات

## 📁 هيكل المشروع

```
web-pos/
├── 📄 index.html              # الصفحة الرئيسية
├── 📄 README.md               # هذا الملف
│
├── 📂 css/                    # ملفات التنسيق
│   ├── neumorphic.css         # نظام التصميم Neumorphic
│   ├── main.css               # التنسيقات الرئيسية
│   ├── login.css              # تنسيقات تسجيل الدخول
│   ├── dashboard.css          # تنسيقات لوحة التحكم
│   ├── touchscreen.css        # تنسيقات الشاشة اللمسية
│   ├── sales.css              # تنسيقات المبيعات
│   ├── inventory.css          # تنسيقات المخزون
│   ├── customers.css          # تنسيقات العملاء
│   ├── reports.css            # تنسيقات التقارير
│   ├── settings.css           # تنسيقات الإعدادات
│   └── animations.css         # الرسوم المتحركة
│
├── 📂 js/                     # ملفات JavaScript
│   ├── 📂 core/               # الملفات الأساسية
│   │   ├── app.js             # التطبيق الرئيسي
│   │   ├── auth.js            # نظام المصادقة
│   │   ├── storage.js         # نظام التخزين
│   │   ├── utils.js           # الأدوات المساعدة
│   │   └── notifications.js   # نظام الإشعارات
│   │
│   └── 📂 modules/            # وحدات التطبيق
│       ├── dashboard.js       # لوحة التحكم
│       ├── sales.js           # المبيعات
│       ├── inventory.js       # المخزون
│       ├── customers.js       # العملاء
│       ├── suppliers.js       # الموردين
│       ├── promotions.js      # العروض والخصومات
│       ├── reports.js         # التقارير
│       ├── analytics.js       # التحليلات
│       ├── settings.js        # الإعدادات
│       ├── touchscreen.js     # الشاشة اللمسية
│       └── quickCashier.js    # الكاشير السريع
│
└── 📂 assets/                 # الموارد
    ├── 📂 images/             # الصور
    ├── 📂 icons/              # الأيقونات
    └── 📂 fonts/              # الخطوط
```

## 🛠️ التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التنسيق والتصميم
- **JavaScript ES6+** - المنطق والتفاعل
- **Bootstrap 5** - الإطار والأيقونات
- **Chart.js** - الرسوم البيانية

### تطبيق سطح المكتب
- **Electron** - إطار تطبيقات سطح المكتب
- **Node.js** - بيئة تشغيل JavaScript
- **Electron Builder** - أداة بناء وتوزيع التطبيقات
- **Native APIs** - واجهات برمجة النظام الأصلية

### التصميم
- **Neumorphic Design** - نمط التصميم العصري
- **RTL Support** - دعم اللغة العربية
- **Responsive Design** - تصميم متجاوب
- **CSS Grid & Flexbox** - تخطيط متقدم

### التخزين
- **localStorage** - تخزين محلي
- **JSON** - تنسيق البيانات
- **Compression** - ضغط البيانات
- **Encryption** - تشفير البيانات

## 📊 الوحدات والميزات

### 🏠 لوحة التحكم
- إحصائيات مبيعات اليوم
- رسوم بيانية تفاعلية
- آخر المبيعات
- تنبيهات المخزون المنخفض

### 🛒 المبيعات
- بحث سريع عن المنتجات
- إدارة سلة التسوق
- حساب الضرائب والخصومات
- طرق دفع متعددة
- طباعة الفواتير

### ⚡ الكاشير السريع
- واجهة مبسطة
- أزرار منتجات سريعة
- اختصارات لوحة المفاتيح
- عمليات سريعة

### 📱 الشاشة اللمسية
- محسنة للأجهزة اللوحية
- أزرار كبيرة وسهلة اللمس
- تفاعل سلس
- تصميم بديهي

### 📦 إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع المخزون
- تنبيهات النفاد
- إدارة الفئات

### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- تاريخ المشتريات
- نقاط الولاء
- تقارير العملاء

### 🏭 إدارة الموردين
- معلومات الموردين
- طلبات الشراء
- تقييم الأداء
- تقارير الموردين

### 🎯 العروض والخصومات
- إنشاء عروض ترويجية
- خصومات متنوعة
- قوالب جاهزة
- جدولة العروض

### 📈 التقارير والتحليلات
- تقارير المبيعات
- تحليل الأرباح
- إحصائيات المنتجات
- تقارير مخصصة

### ⚙️ الإعدادات
- إعدادات الشركة
- إعدادات النظام
- إدارة المستخدمين
- النسخ الاحتياطية

### 🖥️ ميزات تطبيق سطح المكتب الإضافية
- **شاشة بداية جميلة** مع رسوم متحركة
- **قوائم تطبيق أصلية** (ملف، تحرير، عرض، مساعدة)
- **اختصارات لوحة المفاتيح** للوصول السريع
- **حوارات حفظ/فتح** لاستيراد وتصدير البيانات
- **طباعة محسنة** للفواتير والتقارير
- **تصدير متقدم** إلى PDF, Excel, CSV
- **أمان محسن** مع بيئة معزولة
- **أداء أفضل** بدون متصفح
- **تكامل النظام** مع شريط المهام وسطح المكتب
- **تحديثات تلقائية** (في الإصدارات المستقبلية)

## 🔧 التخصيص والتطوير

### إضافة وحدة جديدة
```javascript
// js/modules/newModule.js
export class NewModule {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
    }

    async render() {
        // تنفيذ الوحدة
    }
}
```

### تخصيص التصميم
```css
/* css/custom.css */
:root {
    --neu-primary: #your-color;
    --neu-bg: #your-bg-color;
}
```

### إضافة لغة جديدة
```javascript
// js/core/i18n.js
const translations = {
    ar: { /* النصوص العربية */ },
    en: { /* English texts */ }
};
```

## 🔒 الأمان

### تشفير البيانات
- كلمات المرور مشفرة بـ SHA-256
- بيانات حساسة مشفرة في التخزين
- جلسات آمنة مع انتهاء صلاحية

### صلاحيات المستخدمين
- **المدير**: جميع الصلاحيات
- **المدير المساعد**: صلاحيات محدودة
- **الكاشير**: المبيعات والعملاء فقط

### حماية البيانات
- نسخ احتياطية تلقائية
- تشفير البيانات الحساسة
- تسجيل العمليات والأنشطة

## 📱 التوافق

### 🖥️ أنظمة التشغيل المدعومة (تطبيق سطح المكتب)
- ✅ **Windows 7/8/10/11** (32-bit و 64-bit)
- ✅ **macOS 10.12+** (Intel و Apple Silicon)
- ✅ **Linux** (Ubuntu, Debian, Fedora, CentOS)

### 🌐 المتصفحات المدعومة (تطبيق ويب)
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### 💻 الأجهزة المدعومة
- 🖥️ أجهزة الكمبيوتر المكتبية
- 💻 أجهزة الكمبيوتر المحمولة
- 📱 الهواتف الذكية (تطبيق ويب)
- 📱 الأجهزة اللوحية (تطبيق ويب)

### 📐 أحجام الشاشات
- 🖥️ شاشات كبيرة (1920px+)
- 💻 شاشات متوسطة (1024px-1919px)
- 📱 شاشات صغيرة (768px-1023px)
- 📱 شاشات صغيرة جداً (أقل من 768px)

### ⚙️ متطلبات النظام (تطبيق سطح المكتب)
- **المعالج**: Intel/AMD 1.6 GHz أو أسرع
- **الذاكرة**: 2 GB RAM (4 GB موصى به)
- **التخزين**: 500 MB مساحة فارغة
- **الشاشة**: 1024x768 أو أعلى

## 🚀 الأداء

### تحسينات الأداء
- تحميل كسول للوحدات
- ضغط البيانات
- تخزين مؤقت ذكي
- تحسين الصور

### سرعة التحميل
- أقل من 2 ثانية للتحميل الأولي
- أقل من 500ms للتنقل بين الصفحات
- استجابة فورية للتفاعلات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **لا يعمل التطبيق**: تأكد من تشغيل خادم محلي
2. **البيانات لا تحفظ**: فحص دعم localStorage
3. **الواجهة لا تظهر**: فحص وحدة التحكم للأخطاء

### أدوات التشخيص
```javascript
// فحص حالة التطبيق
console.log('App State:', window.app);
console.log('Storage:', localStorage);
console.log('User:', window.app.getCurrentUser());
```

## 📞 الدعم والمساعدة

### التوثيق
- 📖 [دليل المستخدم](docs/user-guide.md)
- 🔧 [دليل المطور](docs/developer-guide.md)
- 🎨 [دليل التصميم](docs/design-guide.md)

### المجتمع
- 💬 [منتدى النقاش](https://github.com/your-repo/discussions)
- 🐛 [تقرير الأخطاء](https://github.com/your-repo/issues)
- 💡 [طلب ميزات](https://github.com/your-repo/issues/new)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **Bootstrap** - للإطار والأيقونات
- **Chart.js** - للرسوم البيانية
- **Google Fonts** - للخطوط العربية
- **المجتمع العربي** - للدعم والتشجيع

---

**تم تطويره بـ ❤️ للمجتمع العربي**

**النسخة**: 1.0.0  
**آخر تحديث**: 2025-06-15
