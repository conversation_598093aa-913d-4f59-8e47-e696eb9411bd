/**
 * وحدة الكاشير السريع
 * Quick Cashier Module
 */

export class QuickCashier {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.quickProducts = [];
        this.cart = [];
    }

    /**
     * عرض واجهة الكاشير السريع
     */
    async render() {
        try {
            await this.loadData();
            this.createQuickCashierInterface();
            this.bindEvents();
            this.loadQuickProducts();
            this.updateCart();
        } catch (error) {
            console.error('خطأ في عرض الكاشير السريع:', error);
            this.notifications.error('حدث خطأ في تحميل الكاشير السريع');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        const products = this.storage.get('products') || [];
        // أخذ أكثر المنتجات مبيعاً أو المنتجات الأكثر استخداماً
        this.quickProducts = products
            .filter(p => p.isActive && p.stock > 0)
            .slice(0, 12); // أول 12 منتج
    }

    /**
     * إنشاء واجهة الكاشير السريع
     */
    createQuickCashierInterface() {
        const container = document.getElementById('quickCashier-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="quick-cashier-container">
                <div class="quick-header neumorphic-card">
                    <h2><i class="bi bi-lightning"></i> الكاشير السريع</h2>
                    <div class="quick-search">
                        <input type="text" id="quickSearch" class="form-control neumorphic-input" 
                               placeholder="بحث سريع أو مسح الباركود...">
                        <button class="btn neumorphic-btn" onclick="quickCashierModule.scanBarcode()">
                            <i class="bi bi-upc-scan"></i>
                        </button>
                    </div>
                </div>

                <div class="quick-content">
                    <!-- Quick Products -->
                    <div class="quick-products-section neumorphic-card">
                        <h4><i class="bi bi-grid-3x3-gap"></i> المنتجات السريعة</h4>
                        <div class="quick-products-grid" id="quickProductsGrid">
                            <!-- Quick products will be loaded here -->
                        </div>
                    </div>

                    <!-- Quick Cart -->
                    <div class="quick-cart-section neumorphic-card">
                        <div class="cart-header">
                            <h4><i class="bi bi-cart3"></i> الفاتورة السريعة</h4>
                            <div class="cart-info">
                                <span id="quickCartCount">0 منتج</span>
                            </div>
                        </div>

                        <div class="quick-cart-items" id="quickCartItems">
                            <!-- Cart items will be loaded here -->
                        </div>

                        <div class="quick-summary">
                            <div class="summary-line">
                                <span>المجموع:</span>
                                <span id="quickTotal" class="total-amount">0.00 ر.س</span>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <button class="btn neumorphic-btn btn-secondary" onclick="quickCashierModule.clearCart()">
                                <i class="bi bi-trash"></i>
                                مسح
                            </button>
                            <button class="btn neumorphic-btn btn-warning" onclick="quickCashierModule.holdSale()">
                                <i class="bi bi-pause"></i>
                                تعليق
                            </button>
                            <button class="btn neumorphic-btn btn-success" onclick="quickCashierModule.quickCheckout()" 
                                    id="quickCheckoutBtn" disabled>
                                <i class="bi bi-check-circle"></i>
                                دفع سريع
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Payment Modal -->
                <div class="modal fade" id="quickPaymentModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content neumorphic-modal">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-credit-card"></i>
                                    دفع سريع
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="payment-amount">
                                    <h3>المبلغ المطلوب</h3>
                                    <div class="amount-display" id="paymentAmount">0.00 دج</div>
                                </div>
                                
                                <div class="payment-methods">
                                    <h5>طريقة الدفع</h5>
                                    <div class="payment-options">
                                        <button class="payment-btn active" data-method="cash">
                                            <i class="bi bi-cash"></i>
                                            نقدي
                                        </button>
                                        <button class="payment-btn" data-method="card">
                                            <i class="bi bi-credit-card"></i>
                                            بطاقة
                                        </button>
                                        <button class="payment-btn" data-method="transfer">
                                            <i class="bi bi-bank"></i>
                                            تحويل
                                        </button>
                                    </div>
                                </div>

                                <div class="cash-payment" id="cashPayment">
                                    <div class="mb-3">
                                        <label class="form-label">المبلغ المدفوع</label>
                                        <input type="number" id="paidAmount" class="form-control neumorphic-input" 
                                               step="0.01" min="0">
                                    </div>
                                    <div class="change-amount">
                                        <span>الباقي:</span>
                                        <span id="changeAmount">0.00 دج</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary neumorphic-btn" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle"></i>
                                    إلغاء
                                </button>
                                <button type="button" class="btn btn-success neumorphic-btn" onclick="quickCashierModule.completeSale()">
                                    <i class="bi bi-check-circle"></i>
                                    إتمام البيع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث السريع
        const quickSearch = document.getElementById('quickSearch');
        if (quickSearch) {
            quickSearch.addEventListener('input', this.quickSearch.bind(this));
            quickSearch.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.processQuickSearch();
                }
            });
        }

        // حساب الباقي
        const paidAmount = document.getElementById('paidAmount');
        if (paidAmount) {
            paidAmount.addEventListener('input', this.calculateChange.bind(this));
        }

        // طرق الدفع
        document.addEventListener('click', (e) => {
            if (e.target.closest('.payment-btn')) {
                const btn = e.target.closest('.payment-btn');
                document.querySelectorAll('.payment-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const method = btn.dataset.method;
                const cashPayment = document.getElementById('cashPayment');
                if (cashPayment) {
                    cashPayment.style.display = method === 'cash' ? 'block' : 'none';
                }
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

        // ربط الوحدة بالنافذة
        window.quickCashierModule = this;
    }

    /**
     * تحميل المنتجات السريعة
     */
    loadQuickProducts() {
        const container = document.getElementById('quickProductsGrid');
        if (!container) return;

        if (this.quickProducts.length === 0) {
            container.innerHTML = `
                <div class="no-products">
                    <i class="bi bi-box"></i>
                    <p>لا توجد منتجات متاحة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.quickProducts.map(product => `
            <div class="quick-product-btn neumorphic-card" onclick="quickCashierModule.addToCart(${product.id})">
                <div class="product-icon">
                    <i class="bi bi-box"></i>
                </div>
                <div class="product-name">${product.name}</div>
                <div class="product-price">${this.utils.formatCurrency(product.sellingPrice)}</div>
            </div>
        `).join('');
    }

    /**
     * البحث السريع
     */
    quickSearch() {
        const searchTerm = document.getElementById('quickSearch').value.trim();
        if (searchTerm.length < 2) return;

        // البحث في المنتجات
        const products = this.storage.get('products') || [];
        const results = products.filter(product => 
            product.isActive && 
            product.stock > 0 && 
            (this.utils.arabicSearch(product.name, searchTerm) || 
             product.barcode.includes(searchTerm))
        );

        if (results.length === 1) {
            // إضافة المنتج مباشرة إذا كان هناك نتيجة واحدة فقط
            this.addToCart(results[0].id);
            document.getElementById('quickSearch').value = '';
        } else if (results.length > 1) {
            // عرض النتائج للاختيار
            this.showSearchResults(results);
        }
    }

    /**
     * معالجة البحث السريع
     */
    processQuickSearch() {
        const searchTerm = document.getElementById('quickSearch').value.trim();
        if (!searchTerm) return;

        // محاولة العثور على المنتج بالباركود أولاً
        const products = this.storage.get('products') || [];
        const product = products.find(p => 
            p.isActive && 
            p.stock > 0 && 
            p.barcode === searchTerm
        );

        if (product) {
            this.addToCart(product.id);
            document.getElementById('quickSearch').value = '';
        } else {
            this.quickSearch();
        }
    }

    /**
     * عرض نتائج البحث
     */
    showSearchResults(results) {
        // يمكن تنفيذ نافذة منبثقة لعرض النتائج
        console.log('نتائج البحث:', results);
    }

    /**
     * إضافة منتج للسلة
     */
    addToCart(productId) {
        const products = this.storage.get('products') || [];
        const product = products.find(p => p.id === productId);
        
        if (!product) {
            this.notifications.error('المنتج غير موجود');
            return;
        }

        if (product.stock <= 0) {
            this.notifications.warning('المنتج غير متوفر');
            return;
        }

        // فحص إذا كان المنتج موجود في السلة
        const existingItem = this.cart.find(item => item.productId === productId);
        
        if (existingItem) {
            if (existingItem.quantity < product.stock) {
                existingItem.quantity++;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                this.notifications.warning('لا يمكن إضافة كمية أكثر من المتوفر');
                return;
            }
        } else {
            this.cart.push({
                productId: product.id,
                name: product.name,
                price: product.sellingPrice,
                quantity: 1,
                total: product.sellingPrice,
                unit: product.unit
            });
        }

        this.updateCart();
        this.utils.playSound('success');
    }

    /**
     * تحديث كمية منتج
     */
    updateQuantity(productId, change) {
        const cartItem = this.cart.find(item => item.productId === productId);
        if (!cartItem) return;

        const products = this.storage.get('products') || [];
        const product = products.find(p => p.id === productId);
        if (!product) return;

        const newQuantity = cartItem.quantity + change;

        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }

        if (newQuantity > product.stock) {
            this.notifications.warning('الكمية المطلوبة أكبر من المتوفر');
            return;
        }

        cartItem.quantity = newQuantity;
        cartItem.total = cartItem.quantity * cartItem.price;

        this.updateCart();
    }

    /**
     * حذف منتج من السلة
     */
    removeFromCart(productId) {
        const index = this.cart.findIndex(item => item.productId === productId);
        if (index !== -1) {
            this.cart.splice(index, 1);
            this.updateCart();
        }
    }

    /**
     * تحديث عرض السلة
     */
    updateCart() {
        this.updateCartItems();
        this.updateCartSummary();
    }

    /**
     * تحديث عناصر السلة
     */
    updateCartItems() {
        const container = document.getElementById('quickCartItems');
        if (!container) return;

        if (this.cart.length === 0) {
            container.innerHTML = `
                <div class="empty-cart">
                    <i class="bi bi-cart-x"></i>
                    <p>السلة فارغة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.cart.map(item => `
            <div class="quick-cart-item">
                <div class="item-info">
                    <div class="item-name">${item.name}</div>
                    <div class="item-price">${this.utils.formatCurrency(item.price)}</div>
                </div>
                <div class="item-controls">
                    <button class="qty-btn" onclick="quickCashierModule.updateQuantity(${item.productId}, -1)">
                        <i class="bi bi-dash"></i>
                    </button>
                    <span class="qty-display">${item.quantity}</span>
                    <button class="qty-btn" onclick="quickCashierModule.updateQuantity(${item.productId}, 1)">
                        <i class="bi bi-plus"></i>
                    </button>
                </div>
                <div class="item-total">${this.utils.formatCurrency(item.total)}</div>
                <button class="remove-btn" onclick="quickCashierModule.removeFromCart(${item.productId})">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `).join('');
    }

    /**
     * تحديث ملخص السلة
     */
    updateCartSummary() {
        const total = this.utils.sum(this.cart, 'total');
        const itemCount = this.utils.sum(this.cart, 'quantity');

        document.getElementById('quickTotal').textContent = this.utils.formatCurrency(total);
        document.getElementById('quickCartCount').textContent = `${itemCount} منتج`;

        // تفعيل/تعطيل زر الدفع
        const checkoutBtn = document.getElementById('quickCheckoutBtn');
        if (checkoutBtn) {
            checkoutBtn.disabled = this.cart.length === 0;
        }
    }

    /**
     * مسح السلة
     */
    clearCart() {
        if (this.cart.length === 0) return;

        this.cart = [];
        this.updateCart();
        this.notifications.info('تم مسح السلة');
    }

    /**
     * تعليق البيع
     */
    holdSale() {
        if (this.cart.length === 0) {
            this.notifications.warning('السلة فارغة');
            return;
        }

        // حفظ البيع المعلق
        const heldSales = this.storage.get('heldSales') || [];
        const heldSale = {
            id: Date.now(),
            items: [...this.cart],
            createdAt: new Date().toISOString(),
            cashier: this.app.getCurrentUser().fullName
        };

        heldSales.push(heldSale);
        this.storage.set('heldSales', heldSales);

        this.clearCart();
        this.notifications.success('تم تعليق البيع');
    }

    /**
     * دفع سريع
     */
    quickCheckout() {
        if (this.cart.length === 0) {
            this.notifications.warning('السلة فارغة');
            return;
        }

        const total = this.utils.sum(this.cart, 'total');
        document.getElementById('paymentAmount').textContent = this.utils.formatCurrency(total);
        document.getElementById('paidAmount').value = total.toFixed(2);
        
        this.calculateChange();

        const modal = new bootstrap.Modal(document.getElementById('quickPaymentModal'));
        modal.show();
    }

    /**
     * حساب الباقي
     */
    calculateChange() {
        const total = this.utils.sum(this.cart, 'total');
        const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
        const change = paid - total;

        document.getElementById('changeAmount').textContent = this.utils.formatCurrency(Math.max(0, change));
    }

    /**
     * إتمام البيع
     */
    async completeSale() {
        try {
            const total = this.utils.sum(this.cart, 'total');
            const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
            const paymentMethod = document.querySelector('.payment-btn.active').dataset.method;

            if (paymentMethod === 'cash' && paid < total) {
                this.notifications.error('المبلغ المدفوع أقل من المطلوب');
                return;
            }

            // إنشاء الفاتورة
            const sale = {
                id: this.generateSaleId(),
                number: this.generateSaleNumber(),
                items: [...this.cart],
                subtotal: total,
                discount: 0,
                tax: 0,
                total: total,
                customerId: null,
                customerName: 'عميل نقدي',
                paymentMethod: paymentMethod,
                paidAmount: paid,
                changeAmount: Math.max(0, paid - total),
                cashierId: this.app.getCurrentUser().id,
                cashierName: this.app.getCurrentUser().fullName,
                createdAt: new Date().toISOString(),
                status: 'completed'
            };

            // حفظ الفاتورة
            const sales = this.storage.get('sales') || [];
            sales.unshift(sale);
            this.storage.set('sales', sales);

            // تحديث المخزون
            await this.updateInventory(this.cart);

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('quickPaymentModal'));
            modal.hide();

            // مسح السلة
            this.clearCart();

            this.notifications.success('تم إتمام البيع بنجاح');
            this.utils.playSound('success');

        } catch (error) {
            console.error('خطأ في إتمام البيع:', error);
            this.notifications.error('حدث خطأ في إتمام البيع');
        }
    }

    /**
     * تحديث المخزون
     */
    async updateInventory(items) {
        const products = this.storage.get('products') || [];
        
        items.forEach(item => {
            const productIndex = products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                products[productIndex].stock -= item.quantity;
            }
        });

        this.storage.set('products', products);
    }

    /**
     * مسح الباركود
     */
    scanBarcode() {
        this.notifications.info('ميزة مسح الباركود قيد التطوير');
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(e) {
        // F2: دفع سريع
        if (e.key === 'F2') {
            e.preventDefault();
            this.quickCheckout();
        }
        
        // F3: مسح السلة
        if (e.key === 'F3') {
            e.preventDefault();
            this.clearCart();
        }
        
        // F4: تعليق البيع
        if (e.key === 'F4') {
            e.preventDefault();
            this.holdSale();
        }
    }

    /**
     * توليد رقم فاتورة
     */
    generateSaleNumber() {
        const today = new Date();
        const dateStr = today.toISOString().split('T')[0].replace(/-/g, '');
        const timeStr = today.getTime().toString().slice(-4);
        return `Q${dateStr}${timeStr}`;
    }

    /**
     * توليد معرف فاتورة
     */
    generateSaleId() {
        const sales = this.storage.get('sales') || [];
        const maxId = sales.reduce((max, sale) => Math.max(max, sale.id || 0), 0);
        return maxId + 1;
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadQuickProducts();
        this.clearCart();
    }
}
