/* ===== Login Screen Styles ===== */

.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--neu-bg) 0%, #d5dae2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.login-container {
    width: 100%;
    max-width: 450px;
    padding: 2rem;
    animation: slideInUp 0.6s ease;
}

.login-card {
    padding: 3rem 2.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--neu-primary), var(--neu-info), var(--neu-success));
    border-radius: var(--neu-radius) var(--neu-radius) 0 0;
}

.login-header {
    margin-bottom: 2.5rem;
}

.login-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--neu-primary), var(--neu-info));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    box-shadow: var(--neu-shadow-outset);
    animation: logoFloat 3s ease-in-out infinite;
}

.login-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--neu-text);
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--neu-text), var(--neu-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-subtitle {
    font-size: 1rem;
    color: var(--neu-text-light);
    font-weight: 400;
    margin: 0;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    text-align: right;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--neu-text);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.form-label i {
    color: var(--neu-primary);
    font-size: 1.1rem;
}

.login-form .neumorphic-input {
    width: 100%;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    text-align: right;
    transition: var(--neu-transition);
}

.login-form .neumorphic-input:focus {
    transform: translateY(-2px);
}

.login-btn {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--neu-primary), var(--neu-info));
    color: white;
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    transition: var(--neu-transition);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover), 0 10px 25px rgba(52, 152, 219, 0.3);
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-pressed);
}

.login-demo {
    padding-top: 1.5rem;
    border-top: 1px solid rgba(163, 177, 198, 0.3);
}

.login-demo h6 {
    color: var(--neu-text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.demo-accounts {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.demo-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
    border-radius: var(--neu-radius-sm);
    transition: var(--neu-transition);
    background: var(--neu-bg);
    border: 1px solid rgba(163, 177, 198, 0.3);
    color: var(--neu-text);
}

.demo-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--neu-primary);
    color: var(--neu-primary);
    transform: translateY(-1px);
    box-shadow: var(--neu-shadow-hover);
}

.demo-btn i {
    margin-left: 0.5rem;
}

/* ===== Login Animations ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* ===== Error States ===== */
.login-form .neumorphic-input.error {
    box-shadow: var(--neu-shadow-inset), 0 0 0 2px rgba(231, 76, 60, 0.3);
    animation: shake 0.5s ease;
}

.login-form .error-message {
    color: var(--neu-danger);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    text-align: right;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.login-form .error-message i {
    font-size: 1rem;
}

/* ===== Loading State ===== */
.login-btn.loading {
    pointer-events: none;
    position: relative;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.login-btn.loading span {
    opacity: 0;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
        max-width: 100%;
    }
    
    .login-card {
        padding: 2rem 1.5rem;
    }
    
    .login-logo {
        width: 70px;
        height: 70px;
        font-size: 2rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
    
    .login-subtitle {
        font-size: 0.9rem;
    }
    
    .demo-accounts {
        flex-direction: column;
    }
    
    .demo-btn {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 1.5rem 1rem;
    }
    
    .login-logo {
        width: 60px;
        height: 60px;
        font-size: 1.75rem;
    }
    
    .login-title {
        font-size: 1.25rem;
    }
    
    .login-form .neumorphic-input {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }
    
    .login-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
    .login-screen {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
}

/* ===== High Contrast Mode ===== */
@media (prefers-contrast: high) {
    .login-card {
        border: 2px solid var(--neu-text);
    }
    
    .login-btn {
        border: 2px solid var(--neu-primary);
    }
    
    .demo-btn {
        border: 2px solid var(--neu-text-light);
    }
}

/* ===== Reduced Motion ===== */
@media (prefers-reduced-motion: reduce) {
    .login-container {
        animation: none;
    }
    
    .login-logo {
        animation: none;
    }
    
    .login-btn::before {
        display: none;
    }
    
    .login-form .neumorphic-input.error {
        animation: none;
    }
}
