/**
 * وحدة إدارة المخزون
 * Inventory Management Module
 */

export class Inventory {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.products = [];
        this.categories = [];
        this.currentProduct = null;
    }

    /**
     * عرض واجهة المخزون
     */
    async render() {
        try {
            await this.loadData();
            this.createInventoryInterface();
            this.bindEvents();
            this.loadProductsList();
        } catch (error) {
            console.error('خطأ في عرض واجهة المخزون:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة المخزون');
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        this.products = this.storage.get('products') || [];
        this.categories = this.storage.get('categories') || ['إلكترونيات', 'ملابس', 'أغذية', 'أدوات منزلية'];
    }

    /**
     * إنشاء واجهة المخزون
     */
    createInventoryInterface() {
        const container = document.getElementById('inventory-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="inventory-container">
                <div class="inventory-header neumorphic-card">
                    <h2><i class="bi bi-boxes"></i> إدارة المخزون</h2>
                    <div class="inventory-actions">
                        <button class="btn neumorphic-btn btn-primary" onclick="inventoryModule.showAddProductModal()">
                            <i class="bi bi-plus-circle"></i>
                            إضافة منتج جديد
                        </button>
                        <button class="btn neumorphic-btn" onclick="inventoryModule.exportProducts()">
                            <i class="bi bi-download"></i>
                            تصدير
                        </button>
                        <button class="btn neumorphic-btn" onclick="inventoryModule.importProducts()">
                            <i class="bi bi-upload"></i>
                            استيراد
                        </button>
                    </div>
                </div>

                <div class="inventory-filters neumorphic-card">
                    <div class="filter-controls">
                        <div class="search-group">
                            <input type="text" id="productSearchInput" class="form-control neumorphic-input" 
                                   placeholder="البحث بالاسم أو الباركود...">
                        </div>
                        <div class="filter-group">
                            <select id="categoryFilterSelect" class="form-select neumorphic-select">
                                <option value="">جميع الفئات</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select id="stockFilterSelect" class="form-select neumorphic-select">
                                <option value="">جميع المنتجات</option>
                                <option value="in-stock">متوفر</option>
                                <option value="low-stock">مخزون منخفض</option>
                                <option value="out-of-stock">نفد المخزون</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="products-table-container neumorphic-card">
                    <div class="table-responsive">
                        <table class="table neumorphic-table">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>الباركود</th>
                                    <th>الفئة</th>
                                    <th>سعر الشراء</th>
                                    <th>سعر البيع</th>
                                    <th>المخزون</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add/Edit Product Modal -->
            <div class="modal fade" id="productModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content neumorphic-modal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="productModalTitle">
                                <i class="bi bi-box"></i>
                                إضافة منتج جديد
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="productForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المنتج *</label>
                                            <input type="text" id="productName" class="form-control neumorphic-input" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الباركود</label>
                                            <div class="input-group">
                                                <input type="text" id="productBarcode" class="form-control neumorphic-input">
                                                <button type="button" class="btn neumorphic-btn" onclick="inventoryModule.generateBarcode()">
                                                    <i class="bi bi-arrow-clockwise"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفئة *</label>
                                            <select id="productCategory" class="form-select neumorphic-select" required>
                                                <option value="">اختر الفئة</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الوحدة</label>
                                            <select id="productUnit" class="form-select neumorphic-select">
                                                <option value="قطعة">قطعة</option>
                                                <option value="كيلو">كيلو</option>
                                                <option value="لتر">لتر</option>
                                                <option value="متر">متر</option>
                                                <option value="علبة">علبة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر الشراء *</label>
                                            <input type="number" id="productBuyingPrice" class="form-control neumorphic-input" 
                                                   step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر البيع *</label>
                                            <input type="number" id="productSellingPrice" class="form-control neumorphic-input" 
                                                   step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">هامش الربح</label>
                                            <input type="text" id="profitMargin" class="form-control neumorphic-input" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الكمية الحالية *</label>
                                            <input type="number" id="productStock" class="form-control neumorphic-input" 
                                                   min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" id="productMinStock" class="form-control neumorphic-input" 
                                                   min="0" value="5">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea id="productDescription" class="form-control neumorphic-input" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المورد</label>
                                    <input type="text" id="productSupplier" class="form-control neumorphic-input">
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="productActive" class="form-check-input neumorphic-checkbox" checked>
                                    <label class="form-check-label" for="productActive">
                                        منتج نشط
                                    </label>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary neumorphic-btn" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-primary neumorphic-btn" onclick="inventoryModule.saveProduct()">
                                <i class="bi bi-check-circle"></i>
                                حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // تحميل الفئات في النماذج
        this.loadCategoriesInSelects();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // البحث
        const searchInput = document.getElementById('productSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', this.filterProducts.bind(this));
        }

        // تصفية الفئات
        const categoryFilter = document.getElementById('categoryFilterSelect');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', this.filterProducts.bind(this));
        }

        // تصفية المخزون
        const stockFilter = document.getElementById('stockFilterSelect');
        if (stockFilter) {
            stockFilter.addEventListener('change', this.filterProducts.bind(this));
        }

        // حساب هامش الربح
        const buyingPrice = document.getElementById('productBuyingPrice');
        const sellingPrice = document.getElementById('productSellingPrice');
        if (buyingPrice && sellingPrice) {
            buyingPrice.addEventListener('input', this.calculateProfitMargin.bind(this));
            sellingPrice.addEventListener('input', this.calculateProfitMargin.bind(this));
        }

        // ربط الوحدة بالنافذة
        window.inventoryModule = this;
    }

    /**
     * تحميل قائمة المنتجات
     */
    loadProductsList() {
        this.filterProducts();
    }

    /**
     * تصفية المنتجات
     */
    filterProducts() {
        const searchTerm = document.getElementById('productSearchInput')?.value.toLowerCase() || '';
        const categoryFilter = document.getElementById('categoryFilterSelect')?.value || '';
        const stockFilter = document.getElementById('stockFilterSelect')?.value || '';

        let filteredProducts = this.products;

        // تصفية بالبحث
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product => 
                this.utils.arabicSearch(product.name, searchTerm) ||
                product.barcode.toLowerCase().includes(searchTerm)
            );
        }

        // تصفية بالفئة
        if (categoryFilter) {
            filteredProducts = filteredProducts.filter(product => 
                product.category === categoryFilter
            );
        }

        // تصفية بحالة المخزون
        if (stockFilter) {
            filteredProducts = filteredProducts.filter(product => {
                switch (stockFilter) {
                    case 'in-stock':
                        return product.stock > product.minStock;
                    case 'low-stock':
                        return product.stock <= product.minStock && product.stock > 0;
                    case 'out-of-stock':
                        return product.stock <= 0;
                    default:
                        return true;
                }
            });
        }

        this.displayProducts(filteredProducts);
    }

    /**
     * عرض المنتجات في الجدول
     */
    displayProducts(products) {
        const tbody = document.getElementById('productsTableBody');
        if (!tbody) return;

        if (products.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem; color: var(--neu-text-light);"></i>
                        <p class="mt-2 mb-0">لا توجد منتجات</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = products.map(product => `
            <tr>
                <td>
                    <div class="product-image-placeholder">
                        <i class="bi bi-box"></i>
                    </div>
                </td>
                <td>
                    <div class="product-name">${product.name}</div>
                    <small class="text-muted">${product.description || ''}</small>
                </td>
                <td>${product.barcode}</td>
                <td>
                    <span class="badge neumorphic-badge">${product.category}</span>
                </td>
                <td>${this.utils.formatCurrency(product.buyingPrice)}</td>
                <td>${this.utils.formatCurrency(product.sellingPrice)}</td>
                <td>
                    <span class="stock-info ${this.getStockClass(product)}">
                        ${product.stock} ${product.unit}
                    </span>
                </td>
                <td>
                    <span class="badge ${product.isActive ? 'neumorphic-badge badge-success' : 'neumorphic-badge badge-secondary'}">
                        ${product.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm neumorphic-btn" onclick="inventoryModule.editProduct(${product.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm neumorphic-btn" onclick="inventoryModule.adjustStock(${product.id})" title="تعديل المخزون">
                            <i class="bi bi-plus-minus"></i>
                        </button>
                        <button class="btn btn-sm neumorphic-btn btn-danger" onclick="inventoryModule.deleteProduct(${product.id})" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * الحصول على فئة CSS لحالة المخزون
     */
    getStockClass(product) {
        if (product.stock <= 0) return 'out-of-stock';
        if (product.stock <= product.minStock) return 'low-stock';
        return 'in-stock';
    }

    /**
     * عرض نافذة إضافة منتج
     */
    showAddProductModal() {
        this.currentProduct = null;
        document.getElementById('productModalTitle').innerHTML = '<i class="bi bi-plus-circle"></i> إضافة منتج جديد';
        this.resetProductForm();
        this.generateBarcode();
        
        const modal = new bootstrap.Modal(document.getElementById('productModal'));
        modal.show();
    }

    /**
     * تعديل منتج
     */
    editProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        this.currentProduct = product;
        document.getElementById('productModalTitle').innerHTML = '<i class="bi bi-pencil"></i> تعديل المنتج';
        this.fillProductForm(product);
        
        const modal = new bootstrap.Modal(document.getElementById('productModal'));
        modal.show();
    }

    /**
     * ملء نموذج المنتج
     */
    fillProductForm(product) {
        document.getElementById('productName').value = product.name;
        document.getElementById('productBarcode').value = product.barcode;
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productUnit').value = product.unit;
        document.getElementById('productBuyingPrice').value = product.buyingPrice;
        document.getElementById('productSellingPrice').value = product.sellingPrice;
        document.getElementById('productStock').value = product.stock;
        document.getElementById('productMinStock').value = product.minStock;
        document.getElementById('productDescription').value = product.description || '';
        document.getElementById('productSupplier').value = product.supplier || '';
        document.getElementById('productActive').checked = product.isActive;
        
        this.calculateProfitMargin();
    }

    /**
     * إعادة تعيين نموذج المنتج
     */
    resetProductForm() {
        document.getElementById('productForm').reset();
        document.getElementById('productActive').checked = true;
        document.getElementById('profitMargin').value = '';
    }

    /**
     * حفظ المنتج
     */
    async saveProduct() {
        try {
            const formData = this.getProductFormData();
            
            // التحقق من صحة البيانات
            if (!this.validateProductData(formData)) {
                return;
            }

            if (this.currentProduct) {
                // تحديث منتج موجود
                await this.updateProduct(formData);
            } else {
                // إضافة منتج جديد
                await this.addProduct(formData);
            }

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
            modal.hide();

            // تحديث القائمة
            this.loadProductsList();

        } catch (error) {
            console.error('خطأ في حفظ المنتج:', error);
            this.notifications.error('حدث خطأ في حفظ المنتج');
        }
    }

    /**
     * الحصول على بيانات النموذج
     */
    getProductFormData() {
        return {
            name: document.getElementById('productName').value.trim(),
            barcode: document.getElementById('productBarcode').value.trim(),
            category: document.getElementById('productCategory').value,
            unit: document.getElementById('productUnit').value,
            buyingPrice: parseFloat(document.getElementById('productBuyingPrice').value) || 0,
            sellingPrice: parseFloat(document.getElementById('productSellingPrice').value) || 0,
            stock: parseInt(document.getElementById('productStock').value) || 0,
            minStock: parseInt(document.getElementById('productMinStock').value) || 0,
            description: document.getElementById('productDescription').value.trim(),
            supplier: document.getElementById('productSupplier').value.trim(),
            isActive: document.getElementById('productActive').checked
        };
    }

    /**
     * التحقق من صحة بيانات المنتج
     */
    validateProductData(data) {
        if (!data.name) {
            this.notifications.error('اسم المنتج مطلوب');
            return false;
        }

        if (!data.category) {
            this.notifications.error('فئة المنتج مطلوبة');
            return false;
        }

        if (data.buyingPrice <= 0) {
            this.notifications.error('سعر الشراء يجب أن يكون أكبر من صفر');
            return false;
        }

        if (data.sellingPrice <= 0) {
            this.notifications.error('سعر البيع يجب أن يكون أكبر من صفر');
            return false;
        }

        if (data.sellingPrice <= data.buyingPrice) {
            this.notifications.warning('سعر البيع أقل من سعر الشراء');
        }

        // فحص تكرار الباركود
        if (data.barcode) {
            const existingProduct = this.products.find(p => 
                p.barcode === data.barcode && 
                (!this.currentProduct || p.id !== this.currentProduct.id)
            );
            
            if (existingProduct) {
                this.notifications.error('الباركود موجود بالفعل');
                return false;
            }
        }

        return true;
    }

    /**
     * إضافة منتج جديد
     */
    async addProduct(data) {
        const newProduct = {
            id: this.generateProductId(),
            ...data,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.products.push(newProduct);
        this.storage.set('products', this.products);
        
        this.notifications.success('تم إضافة المنتج بنجاح');
    }

    /**
     * تحديث منتج موجود
     */
    async updateProduct(data) {
        const index = this.products.findIndex(p => p.id === this.currentProduct.id);
        if (index !== -1) {
            this.products[index] = {
                ...this.currentProduct,
                ...data,
                updatedAt: new Date().toISOString()
            };
            
            this.storage.set('products', this.products);
            this.notifications.success('تم تحديث المنتج بنجاح');
        }
    }

    /**
     * حذف منتج
     */
    async deleteProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        const confirmed = await this.notifications.confirm(
            `هل تريد حذف المنتج "${product.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const index = this.products.findIndex(p => p.id === productId);
            if (index !== -1) {
                this.products.splice(index, 1);
                this.storage.set('products', this.products);
                this.loadProductsList();
                this.notifications.success('تم حذف المنتج بنجاح');
            }
        }
    }

    /**
     * توليد معرف منتج جديد
     */
    generateProductId() {
        const maxId = this.products.reduce((max, product) => Math.max(max, product.id || 0), 0);
        return maxId + 1;
    }

    /**
     * توليد باركود
     */
    generateBarcode() {
        const barcode = this.utils.generateBarcode();
        document.getElementById('productBarcode').value = barcode;
    }

    /**
     * حساب هامش الربح
     */
    calculateProfitMargin() {
        const buyingPrice = parseFloat(document.getElementById('productBuyingPrice').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('productSellingPrice').value) || 0;
        
        if (buyingPrice > 0 && sellingPrice > 0) {
            const profit = sellingPrice - buyingPrice;
            const margin = (profit / buyingPrice) * 100;
            document.getElementById('profitMargin').value = `${margin.toFixed(2)}%`;
        } else {
            document.getElementById('profitMargin').value = '';
        }
    }

    /**
     * تحميل الفئات في القوائم المنسدلة
     */
    loadCategoriesInSelects() {
        const categoryFilter = document.getElementById('categoryFilterSelect');
        const productCategory = document.getElementById('productCategory');

        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="">جميع الفئات</option>' +
                this.categories.map(cat => `<option value="${cat}">${cat}</option>`).join('');
        }

        if (productCategory) {
            productCategory.innerHTML = '<option value="">اختر الفئة</option>' +
                this.categories.map(cat => `<option value="${cat}">${cat}</option>`).join('');
        }
    }

    /**
     * تعديل المخزون
     */
    async adjustStock(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        const newStock = await this.notifications.prompt(
            `الكمية الحالية: ${product.stock} ${product.unit}\nأدخل الكمية الجديدة:`,
            'تعديل المخزون',
            product.stock.toString()
        );

        if (newStock !== null) {
            const stockValue = parseInt(newStock);
            if (!isNaN(stockValue) && stockValue >= 0) {
                const index = this.products.findIndex(p => p.id === productId);
                if (index !== -1) {
                    this.products[index].stock = stockValue;
                    this.products[index].updatedAt = new Date().toISOString();
                    this.storage.set('products', this.products);
                    this.loadProductsList();
                    this.notifications.success('تم تحديث المخزون بنجاح');
                }
            } else {
                this.notifications.error('يرجى إدخال رقم صحيح');
            }
        }
    }

    /**
     * تصدير المنتجات
     */
    exportProducts() {
        try {
            const data = {
                products: this.products,
                categories: this.categories,
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            this.utils.downloadFile(dataStr, `products-${new Date().toISOString().split('T')[0]}.json`, 'application/json');
            
            this.notifications.success('تم تصدير المنتجات بنجاح');
        } catch (error) {
            console.error('خطأ في تصدير المنتجات:', error);
            this.notifications.error('حدث خطأ في تصدير المنتجات');
        }
    }

    /**
     * استيراد المنتجات
     */
    importProducts() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = async (e) => {
            try {
                const file = e.target.files[0];
                if (!file) return;

                const content = await this.utils.readFile(file);
                const data = JSON.parse(content);

                if (data.products && Array.isArray(data.products)) {
                    const confirmed = await this.notifications.confirm(
                        `سيتم استيراد ${data.products.length} منتج. هل تريد المتابعة؟`,
                        'تأكيد الاستيراد'
                    );

                    if (confirmed) {
                        this.products = data.products;
                        if (data.categories) {
                            this.categories = data.categories;
                            this.storage.set('categories', this.categories);
                        }
                        
                        this.storage.set('products', this.products);
                        this.loadCategoriesInSelects();
                        this.loadProductsList();
                        this.notifications.success('تم استيراد المنتجات بنجاح');
                    }
                } else {
                    this.notifications.error('ملف غير صحيح');
                }
            } catch (error) {
                console.error('خطأ في استيراد المنتجات:', error);
                this.notifications.error('حدث خطأ في استيراد المنتجات');
            }
        };

        input.click();
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadData();
        this.loadCategoriesInSelects();
        this.loadProductsList();
    }
}
