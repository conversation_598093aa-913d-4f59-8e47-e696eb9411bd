/* ===== Neumorphic Design System ===== */

:root {
    /* Neumorphic Colors */
    --neu-bg: #e0e5ec;
    --neu-shadow-dark: #a3b1c6;
    --neu-shadow-light: #ffffff;
    --neu-text: #2c3e50;
    --neu-text-light: #7f8c8d;
    --neu-primary: #3498db;
    --neu-success: #27ae60;
    --neu-warning: #f39c12;
    --neu-danger: #e74c3c;
    --neu-info: #9b59b6;
    
    /* Neumorphic Shadows */
    --neu-shadow-outset: 9px 9px 16px var(--neu-shadow-dark), -9px -9px 16px var(--neu-shadow-light);
    --neu-shadow-inset: inset 4px 4px 8px var(--neu-shadow-dark), inset -4px -4px 8px var(--neu-shadow-light);
    --neu-shadow-pressed: inset 6px 6px 12px var(--neu-shadow-dark), inset -6px -6px 12px var(--neu-shadow-light);
    --neu-shadow-hover: 6px 6px 12px var(--neu-shadow-dark), -6px -6px 12px var(--neu-shadow-light);
    
    /* Border Radius */
    --neu-radius-sm: 8px;
    --neu-radius: 12px;
    --neu-radius-lg: 20px;
    --neu-radius-xl: 30px;
    
    /* Transitions */
    --neu-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --neu-transition-fast: all 0.15s ease;
}

/* ===== Base Neumorphic Components ===== */

.neumorphic-card {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    border: none;
    transition: var(--neu-transition);
    padding: 1.5rem;
}

.neumorphic-card:hover {
    box-shadow: var(--neu-shadow-hover);
    transform: translateY(-2px);
}

.neumorphic-card.pressed {
    box-shadow: var(--neu-shadow-pressed);
    transform: translateY(1px);
}

.neumorphic-btn {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-text);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--neu-transition);
    position: relative;
    overflow: hidden;
}

.neumorphic-btn:hover {
    box-shadow: var(--neu-shadow-hover);
    transform: translateY(-1px);
    color: var(--neu-text);
}

.neumorphic-btn:active,
.neumorphic-btn.active {
    box-shadow: var(--neu-shadow-pressed);
    transform: translateY(1px);
}

.neumorphic-btn:focus {
    box-shadow: var(--neu-shadow-outset), 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
}

/* Button Variants */
.neumorphic-btn.btn-primary {
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
}

.neumorphic-btn.btn-success {
    background: linear-gradient(145deg, #27ae60, #229954);
    color: white;
}

.neumorphic-btn.btn-warning {
    background: linear-gradient(145deg, #f39c12, #e67e22);
    color: white;
}

.neumorphic-btn.btn-danger {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    color: white;
}

.neumorphic-btn.btn-info {
    background: linear-gradient(145deg, #9b59b6, #8e44ad);
    color: white;
}

/* Input Fields */
.neumorphic-input {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius-sm);
    box-shadow: var(--neu-shadow-inset);
    color: var(--neu-text);
    padding: 0.75rem 1rem;
    transition: var(--neu-transition);
    font-size: 1rem;
}

.neumorphic-input:focus {
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
    background: var(--neu-bg);
    color: var(--neu-text);
}

.neumorphic-input::placeholder {
    color: var(--neu-text-light);
}

/* Select Dropdown */
.neumorphic-select {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius-sm);
    box-shadow: var(--neu-shadow-inset);
    color: var(--neu-text);
    padding: 0.75rem 1rem;
    transition: var(--neu-transition);
}

.neumorphic-select:focus {
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
}

/* Checkbox and Radio */
.neumorphic-checkbox,
.neumorphic-radio {
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--neu-bg);
    border-radius: var(--neu-radius-sm);
    box-shadow: var(--neu-shadow-inset);
    position: relative;
    cursor: pointer;
    transition: var(--neu-transition);
}

.neumorphic-radio {
    border-radius: 50%;
}

.neumorphic-checkbox:checked,
.neumorphic-radio:checked {
    background: var(--neu-primary);
    box-shadow: var(--neu-shadow-outset);
}

.neumorphic-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.neumorphic-radio:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

/* Progress Bar */
.neumorphic-progress {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
    height: 20px;
    overflow: hidden;
}

.neumorphic-progress-bar {
    background: linear-gradient(90deg, var(--neu-primary), var(--neu-info));
    height: 100%;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    transition: width 0.6s ease;
}

/* Switch Toggle */
.neumorphic-switch {
    position: relative;
    width: 60px;
    height: 30px;
    background: var(--neu-bg);
    border-radius: 15px;
    box-shadow: var(--neu-shadow-inset);
    cursor: pointer;
    transition: var(--neu-transition);
}

.neumorphic-switch::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: var(--neu-bg);
    border-radius: 50%;
    box-shadow: var(--neu-shadow-outset);
    transition: var(--neu-transition);
}

.neumorphic-switch.active {
    background: var(--neu-primary);
}

.neumorphic-switch.active::after {
    left: 33px;
    background: white;
}

/* Tabs */
.neumorphic-tabs {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
}

.neumorphic-tab {
    background: transparent;
    border: none;
    border-radius: var(--neu-radius-sm);
    color: var(--neu-text);
    padding: 0.75rem 1.5rem;
    transition: var(--neu-transition);
    cursor: pointer;
    flex: 1;
    text-align: center;
}

.neumorphic-tab:hover {
    background: rgba(52, 152, 219, 0.1);
}

.neumorphic-tab.active {
    background: var(--neu-bg);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-primary);
    font-weight: 600;
}

/* Modal */
.neumorphic-modal {
    background: var(--neu-bg);
    border-radius: var(--neu-radius-lg);
    box-shadow: var(--neu-shadow-outset), 0 20px 40px rgba(0, 0, 0, 0.1);
    border: none;
}

.neumorphic-modal .modal-header {
    background: transparent;
    border-bottom: 1px solid rgba(163, 177, 198, 0.3);
    border-radius: var(--neu-radius-lg) var(--neu-radius-lg) 0 0;
}

.neumorphic-modal .modal-footer {
    background: transparent;
    border-top: 1px solid rgba(163, 177, 198, 0.3);
    border-radius: 0 0 var(--neu-radius-lg) var(--neu-radius-lg);
}

/* Dropdown */
.neumorphic-dropdown {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    padding: 0.5rem 0;
}

.neumorphic-dropdown .dropdown-item {
    color: var(--neu-text);
    padding: 0.75rem 1.5rem;
    transition: var(--neu-transition-fast);
}

.neumorphic-dropdown .dropdown-item:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--neu-primary);
}

.neumorphic-dropdown .dropdown-divider {
    border-color: rgba(163, 177, 198, 0.3);
    margin: 0.5rem 0;
}

/* Table */
.neumorphic-table {
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    overflow: hidden;
}

.neumorphic-table th {
    background: rgba(52, 152, 219, 0.1);
    color: var(--neu-text);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.neumorphic-table td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
}

.neumorphic-table tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* Badge */
.neumorphic-badge {
    background: var(--neu-bg);
    border-radius: var(--neu-radius-sm);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-text);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
}

.neumorphic-badge.badge-primary {
    background: var(--neu-primary);
    color: white;
}

.neumorphic-badge.badge-success {
    background: var(--neu-success);
    color: white;
}

.neumorphic-badge.badge-warning {
    background: var(--neu-warning);
    color: white;
}

.neumorphic-badge.badge-danger {
    background: var(--neu-danger);
    color: white;
}

/* Alert */
.neumorphic-alert {
    background: var(--neu-bg);
    border: none;
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-outset);
    color: var(--neu-text);
    padding: 1rem 1.5rem;
}

.neumorphic-alert.alert-primary {
    background: rgba(52, 152, 219, 0.1);
    color: var(--neu-primary);
}

.neumorphic-alert.alert-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--neu-success);
}

.neumorphic-alert.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--neu-warning);
}

.neumorphic-alert.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--neu-danger);
}

/* Utilities */
.neu-shadow-outset {
    box-shadow: var(--neu-shadow-outset);
}

.neu-shadow-inset {
    box-shadow: var(--neu-shadow-inset);
}

.neu-shadow-pressed {
    box-shadow: var(--neu-shadow-pressed);
}

.neu-shadow-hover {
    box-shadow: var(--neu-shadow-hover);
}

.neu-bg {
    background: var(--neu-bg);
}

.neu-text {
    color: var(--neu-text);
}

.neu-text-light {
    color: var(--neu-text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .neumorphic-card {
        padding: 1rem;
    }
    
    .neumorphic-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .neumorphic-input {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
}
