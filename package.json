{"name": "advanced-pos-system", "version": "1.0.0", "description": "نظام نقطة البيع المتقدم - Advanced POS System with Neumorphic Design", "main": "main.js", "scripts": {"electron": "electron .", "electron-dev": "NODE_ENV=development electron .", "start": "npx serve . -p 8000", "dev": "npx serve . -p 3000 --live", "build": "npm run minify && npm run optimize", "build-electron": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "build-all": "electron-builder --win --mac --linux", "dist": "npm run build && npm run build-electron", "minify": "npm run minify-css && npm run minify-js", "minify-css": "npx clean-css-cli css/*.css -o dist/css/styles.min.css", "minify-js": "npx terser js/**/*.js -o dist/js/app.min.js", "optimize": "npm run optimize-images", "optimize-images": "npx imagemin assets/images/* --out-dir=dist/assets/images", "lint": "npm run lint-js && npm run lint-css", "lint-js": "npx eslint js/**/*.js", "lint-css": "npx stylelint css/*.css", "test": "npm run test-unit && npm run test-e2e", "test-unit": "npx jest", "test-e2e": "npx playwright test", "format": "npx prettier --write .", "validate": "npm run lint && npm run test", "deploy": "npm run build && npm run deploy-gh-pages", "deploy-gh-pages": "npx gh-pages -d dist", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "docs": "npx jsdoc js/**/*.js -d docs/api", "analyze": "npx webpack-bundle-analyzer dist/js/app.min.js", "security": "npm audit && npx snyk test", "update": "npx npm-check-updates -u && npm install"}, "keywords": ["pos", "point-of-sale", "retail", "arabic", "rtl", "neumorphic", "javascript", "html5", "css3", "bootstrap", "offline", "localStorage", "responsive", "touchscreen", "sales", "inventory", "customers", "reports", "analytics"], "author": {"name": "Augment Agent", "email": "<EMAIL>", "url": "https://github.com/augment-agent"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/advanced-pos-system.git"}, "bugs": {"url": "https://github.com/your-username/advanced-pos-system/issues"}, "homepage": "https://your-username.github.io/advanced-pos-system", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"@playwright/test": "^1.40.0", "clean-css-cli": "^5.6.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "gh-pages": "^6.1.0", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0", "jest": "^29.7.0", "jsdoc": "^4.0.2", "npm-check-updates": "^16.14.12", "prettier": "^3.1.1", "serve": "^14.2.1", "snyk": "^1.1266.0", "stylelint": "^16.0.2", "terser": "^5.26.0", "webpack-bundle-analyzer": "^4.10.1"}, "dependencies": {"electron": "^28.0.0"}, "peerDependencies": {"bootstrap": "^5.3.0", "chart.js": "^4.4.0"}, "config": {"port": 8000, "host": "localhost"}, "eslintConfig": {"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "no-var": "error"}}, "stylelint": {"extends": ["stylelint-config-standard"], "rules": {"indentation": 4, "string-quotes": "single", "no-duplicate-selectors": true, "color-hex-case": "lower", "color-hex-length": "short", "selector-combinator-space-after": "always", "selector-attribute-quotes": "always", "selector-attribute-operator-space-before": "never", "selector-attribute-operator-space-after": "never", "selector-attribute-brackets-space-inside": "never", "declaration-block-trailing-semicolon": "always", "declaration-colon-space-before": "never", "declaration-colon-space-after": "always", "number-leading-zero": "always", "function-url-quotes": "always", "font-family-name-quotes": "always-where-recommended", "comment-whitespace-inside": "always", "rule-empty-line-before": "always-multi-line", "selector-pseudo-element-colon-notation": "double", "selector-pseudo-class-parentheses-space-inside": "never", "media-feature-range-operator-space-before": "always", "media-feature-range-operator-space-after": "always", "media-feature-parentheses-space-inside": "never", "media-feature-colon-space-before": "never", "media-feature-colon-space-after": "always"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 4, "useTabs": false}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["js/**/*.js", "!js/**/*.min.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "files": ["index.html", "css/", "js/", "assets/", "README.md", "LICENSE"], "directories": {"doc": "docs", "test": "tests"}, "funding": {"type": "github", "url": "https://github.com/sponsors/your-username"}, "contributors": [{"name": "Augment Agent", "email": "<EMAIL>", "url": "https://github.com/augment-agent"}], "maintainers": [{"name": "Augment Agent", "email": "<EMAIL>", "url": "https://github.com/augment-agent"}], "private": false, "publishConfig": {"access": "public"}, "build": {"appId": "com.advancedpos.app", "productName": "نظام نقطة البيع المتقدم", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!src/**/*", "!docs/**/*", "!tests/**/*", "!coverage/**/*", "!.git/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام نقطة البيع المتقدم", "include": "installer.nsh"}, "portable": {"artifactName": "AdvancedPOS-Portable-${version}-${arch}.${ext}"}}}