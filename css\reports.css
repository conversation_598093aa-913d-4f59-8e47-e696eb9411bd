/**
 * تنسيقات التقارير
 * Reports Styles
 */

/* الحاوية الرئيسية */
.reports-container {
    padding: 1rem;
}

.reports-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.reports-header h2 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* شبكة التقارير */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    padding: 2rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--neu-shadow-hover);
    text-decoration: none;
    color: inherit;
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neu-primary), var(--neu-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    flex-shrink: 0;
}

.report-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--neu-text);
    font-weight: 600;
    font-size: 1.2rem;
}

.report-info p {
    margin: 0;
    color: var(--neu-text-light);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* محتوى التقرير */
.report-content {
    margin-top: 2rem;
    padding: 2rem;
    background: var(--neu-bg);
    border-radius: var(--neu-radius);
    box-shadow: var(--neu-shadow-inset);
}

.report-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--neu-border);
}

.report-header h3 {
    margin: 0;
    color: var(--neu-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
}

.report-actions {
    display: flex;
    gap: 1rem;
}

/* إحصائيات التقرير */
.report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow);
}

.stat-item h4 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--neu-primary);
    direction: ltr;
    text-align: center;
}

.stat-item p {
    margin: 0;
    color: var(--neu-text);
    font-weight: 500;
}

/* جدول التقرير */
.report-table {
    margin-bottom: 2rem;
}

.report-table h5 {
    margin: 0 0 1rem 0;
    color: var(--neu-text);
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--neu-border);
}

.report-table table {
    width: 100%;
    background: var(--neu-bg);
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--neu-radius);
    overflow: hidden;
    box-shadow: var(--neu-shadow-inset);
}

.report-table thead th {
    background: var(--neu-bg-light);
    color: var(--neu-text);
    font-weight: 600;
    padding: 1rem;
    text-align: right;
    border: none;
}

.report-table tbody td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid var(--neu-border);
    color: var(--neu-text);
}

.report-table tbody tr:last-child td {
    border-bottom: none;
}

.report-table tbody tr:hover {
    background: var(--neu-bg-hover);
}

/* التقرير المالي */
.financial-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.summary-section {
    padding: 1.5rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
}

.summary-section h5 {
    margin: 0 0 1rem 0;
    color: var(--neu-primary);
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--neu-border);
}

.financial-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(var(--neu-border-rgb), 0.3);
}

.financial-item:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
}

.financial-item span:first-child {
    color: var(--neu-text);
}

.financial-item span:last-child {
    font-weight: 600;
    direction: ltr;
}

.text-success {
    color: #27ae60 !important;
}

.text-danger {
    color: #e74c3c !important;
}

/* هوامش الربح */
.profit-margin {
    padding: 1.5rem;
    background: var(--neu-bg-light);
    border-radius: var(--neu-radius);
    margin-bottom: 2rem;
}

.profit-margin h5 {
    margin: 0 0 1rem 0;
    color: var(--neu-primary);
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--neu-border);
}

.margin-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(var(--neu-border-rgb), 0.3);
}

.margin-item:last-child {
    border-bottom: none;
}

.margin-item span:first-child {
    color: var(--neu-text);
}

.margin-item span:last-child {
    font-weight: 600;
    color: var(--neu-primary);
    direction: ltr;
}

/* شارات التقرير */
.badge-warning {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.badge-danger {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* حالة فارغة */
.d-none {
    display: none !important;
}

/* التجاوب */
@media (max-width: 1024px) {
    .reports-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .report-card {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .report-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .financial-summary {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .reports-container {
        padding: 0.5rem;
    }
    
    .reports-header,
    .report-content {
        padding: 1rem;
    }
    
    .report-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .report-actions {
        justify-content: center;
    }
    
    .report-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
    
    .stat-item h4 {
        font-size: 1.5rem;
    }
    
    .report-table table,
    .report-table thead th,
    .report-table tbody td {
        font-size: 0.9rem;
        padding: 0.75rem 0.5rem;
    }
    
    .summary-section,
    .profit-margin {
        padding: 1rem;
    }
}

/* تأثيرات الطباعة */
@media print {
    .report-actions {
        display: none !important;
    }
    
    .report-content {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .report-header {
        border-bottom: 2px solid #333;
    }
    
    .report-table table {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .report-table thead th {
        background: #f5f5f5 !important;
        color: #333 !important;
    }
    
    body {
        font-family: Arial, sans-serif;
        direction: rtl;
    }
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إضافية */
.text-muted {
    color: var(--neu-text-light) !important;
}

.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mt-2 {
    margin-top: 0.5rem;
}

.py-4 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}
