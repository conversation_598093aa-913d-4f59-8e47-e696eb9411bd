# دليل البناء - Build Guide

هذا الدليل يوضح كيفية بناء تطبيق سطح المكتب من نظام نقطة البيع المتقدم.

## 📋 المتطلبات الأساسية

### Node.js و npm
```bash
# تحقق من إصدار Node.js (يجب أن يكون 16 أو أحدث)
node --version

# تحقق من إصدار npm
npm --version
```

### Git (اختياري)
```bash
git --version
```

## 🚀 خطوات البناء

### 1. تحضير المشروع
```bash
# الانتقال إلى مجلد المشروع
cd web-pos

# تثبيت التبعيات
npm install
```

### 2. اختبار التطبيق في وضع التطوير
```bash
# تشغيل التطبيق في وضع التطوير
npm run electron-dev
```

### 3. بناء التطبيق للإنتاج

#### بناء لجميع المنصات
```bash
npm run build-all
```

#### بناء لمنصة واحدة
```bash
# Windows (32-bit و 64-bit)
npm run build-win

# macOS (Intel و Apple Silicon)
npm run build-mac

# Linux (64-bit)
npm run build-linux
```

### 4. بناء محدد حسب المعمارية

#### Windows
```bash
# Windows 64-bit فقط
npx electron-builder --win --x64

# Windows 32-bit فقط
npx electron-builder --win --ia32

# Windows ARM64
npx electron-builder --win --arm64
```

#### macOS
```bash
# macOS Intel
npx electron-builder --mac --x64

# macOS Apple Silicon
npx electron-builder --mac --arm64

# macOS Universal (Intel + Apple Silicon)
npx electron-builder --mac --universal
```

#### Linux
```bash
# Linux 64-bit
npx electron-builder --linux --x64

# Linux ARM64
npx electron-builder --linux --arm64

# Linux ARMv7
npx electron-builder --linux --armv7l
```

## 📦 أنواع الحزم المختلفة

### Windows
- **NSIS Installer** (`.exe`) - مثبت كامل مع إعداد
- **Portable** (`.exe`) - نسخة محمولة بدون تثبيت
- **MSI** (`.msi`) - مثبت Windows MSI
- **AppX** (`.appx`) - حزمة Microsoft Store

### macOS
- **DMG** (`.dmg`) - صورة قرص macOS
- **PKG** (`.pkg`) - مثبت macOS
- **ZIP** (`.zip`) - أرشيف مضغوط

### Linux
- **AppImage** (`.AppImage`) - تطبيق محمول
- **DEB** (`.deb`) - حزمة Debian/Ubuntu
- **RPM** (`.rpm`) - حزمة Red Hat/Fedora
- **Snap** (`.snap`) - حزمة Snap
- **Flatpak** - حزمة Flatpak

## 🔧 إعدادات البناء المتقدمة

### تخصيص إعدادات البناء
يمكنك تعديل إعدادات البناء في `package.json` تحت قسم `build`:

```json
{
  "build": {
    "appId": "com.advancedpos.app",
    "productName": "نظام نقطة البيع المتقدم",
    "directories": {
      "output": "dist"
    }
  }
}
```

### متغيرات البيئة
```bash
# تعيين بيئة الإنتاج
export NODE_ENV=production

# تعيين مجلد الإخراج
export BUILD_OUTPUT_DIR=./releases

# تعطيل التوقيع الرقمي (للاختبار)
export CSC_IDENTITY_AUTO_DISCOVERY=false
```

### التوقيع الرقمي

#### Windows
```bash
# تعيين شهادة Windows
export CSC_LINK=path/to/certificate.p12
export CSC_KEY_PASSWORD=your_password
```

#### macOS
```bash
# تعيين شهادة Apple
export CSC_LINK=path/to/certificate.p12
export CSC_KEY_PASSWORD=your_password
export APPLE_ID=your_apple_id
export APPLE_ID_PASSWORD=your_app_password
```

## 🐛 حل المشاكل الشائعة

### مشكلة: فشل في تثبيت electron
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### مشكلة: فشل البناء بسبب نقص الذاكرة
```bash
# زيادة حد الذاكرة
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build-all
```

### مشكلة: فشل البناء على Linux
```bash
# تثبيت التبعيات المطلوبة
sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2-dev
```

### مشكلة: فشل البناء على macOS
```bash
# تثبيت Xcode Command Line Tools
xcode-select --install
```

## 📊 تحسين حجم التطبيق

### تقليل حجم الحزمة
```bash
# استبعاد ملفات غير ضرورية
# أضف في package.json:
"build": {
  "files": [
    "**/*",
    "!node_modules/**/*",
    "!src/**/*",
    "!docs/**/*"
  ]
}
```

### ضغط الأصول
```bash
# ضغط الصور
npm install -g imagemin-cli
imagemin assets/*.png --out-dir=assets/compressed

# تصغير CSS و JS
npm install -g clean-css-cli terser
cleancss -o style.min.css style.css
terser script.js -o script.min.js
```

## 🚀 النشر التلقائي

### GitHub Actions
إنشاء ملف `.github/workflows/build.yml`:

```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
    
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - run: npm install
    - run: npm run build
    
    - uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.os }}-build
        path: dist/
```

## 📝 ملاحظات مهمة

1. **الأيقونات**: تأكد من وجود أيقونات بالتنسيقات الصحيحة في مجلد `assets/`
2. **الشهادات**: للتوزيع الرسمي، ستحتاج شهادات توقيع رقمي
3. **الاختبار**: اختبر التطبيق على جميع المنصات المستهدفة
4. **الحجم**: راقب حجم التطبيق وحسّنه حسب الحاجة
5. **التحديثات**: فكر في نظام تحديث تلقائي للإصدارات المستقبلية

## 🔗 روابط مفيدة

- [Electron Builder Documentation](https://www.electron.build/)
- [Electron Documentation](https://www.electronjs.org/docs)
- [Node.js Downloads](https://nodejs.org/en/download/)
- [Code Signing Guide](https://www.electron.build/code-signing)
