/**
 * ملف التكوين الرئيسي للنظام
 * Main Configuration File
 */

window.POS_CONFIG = {
    // إعدادات التطبيق الأساسية
    app: {
        name: 'نظام نقطة البيع المتقدم',
        version: '1.0.0',
        author: 'Augment Agent',
        description: 'نظام نقطة بيع متقدم مع تصميم Neumorphic',
        language: 'ar',
        direction: 'rtl'
    },

    // إعدادات الواجهة
    ui: {
        theme: 'neumorphic',
        primaryColor: '#3498db',
        secondaryColor: '#2ecc71',
        backgroundColor: '#e0e5ec',
        animationDuration: 300,
        showAnimations: true,
        touchMode: false // سيتم تحديدها تلقائياً
    },

    // إعدادات النظام
    system: {
        autoSave: true,
        autoSaveInterval: 30000, // 30 ثانية
        sessionTimeout: 3600000, // ساعة واحدة
        maxLoginAttempts: 5,
        lockoutDuration: 300000, // 5 دقائق
        enableBackup: true,
        backupInterval: 86400000 // 24 ساعة
    },

    // إعدادات المبيعات
    sales: {
        defaultTaxRate: 19, // 19% (ضريبة الجزائر)
        currency: 'DZD',
        currencySymbol: 'دج',
        currencyPosition: 'after', // before أو after
        decimalPlaces: 2,
        allowNegativeStock: false,
        requireCustomerInfo: false,
        printReceiptAutomatically: true,
        enableBarcode: true,
        enableQuickSale: true
    },

    // إعدادات المخزون
    inventory: {
        lowStockThreshold: 10,
        enableStockAlerts: true,
        autoUpdateStock: true,
        enableBatchTracking: false,
        enableExpiryTracking: false,
        defaultCategory: 'عام',
        enableSupplierManagement: true
    },

    // إعدادات العملاء
    customers: {
        enableLoyaltyProgram: true,
        loyaltyPointsRate: 1, // نقطة لكل دينار
        loyaltyRedemptionRate: 0.01, // 1 نقطة = 0.01 دينار
        requirePhoneNumber: false,
        enableCustomerHistory: true,
        maxCustomerHistory: 100
    },

    // إعدادات التقارير
    reports: {
        defaultDateRange: 'month',
        enableAdvancedAnalytics: true,
        exportFormats: ['pdf', 'excel', 'csv'],
        enableScheduledReports: false,
        reportRetentionDays: 365
    },

    // إعدادات الطباعة
    printing: {
        enableThermalPrinting: false,
        thermalPrinterWidth: 80, // mm
        receiptTemplate: 'default',
        printLogo: true,
        printBarcode: true,
        printFooter: true,
        copies: 1
    },

    // إعدادات الأمان
    security: {
        enablePasswordPolicy: true,
        minPasswordLength: 6,
        requireSpecialChars: false,
        enableTwoFactor: false,
        sessionEncryption: true,
        auditLog: true,
        maxAuditLogEntries: 1000
    },

    // إعدادات التخزين
    storage: {
        type: 'localStorage', // localStorage أو indexedDB
        enableCompression: false,
        maxStorageSize: 50, // MB
        enableCloudSync: false,
        cloudProvider: null
    },

    // إعدادات الإشعارات
    notifications: {
        enableSound: true,
        enableDesktop: false,
        enableEmail: false,
        position: 'top-right',
        duration: 5000,
        maxNotifications: 5
    },

    // إعدادات الشاشة اللمسية
    touchscreen: {
        enableGestures: true,
        buttonSize: 'large',
        enableHapticFeedback: false,
        swipeThreshold: 50,
        longPressDelay: 500
    },

    // إعدادات التطوير
    development: {
        enableDebugMode: false,
        enableConsoleLogging: true,
        enablePerformanceMonitoring: false,
        mockData: false,
        apiEndpoint: null
    },

    // إعدادات التكامل
    integration: {
        enableAPI: false,
        apiKey: null,
        webhooks: [],
        enableExternalPayments: false,
        paymentProviders: []
    },

    // إعدادات الشركة الافتراضية
    company: {
        name: 'شركتي',
        address: '',
        phone: '',
        email: '',
        website: '',
        taxNumber: '',
        logo: null,
        currency: 'DZD'
    },

    // إعدادات الميزات
    features: {
        enableSuppliers: true,
        enablePromotions: true,
        enableAnalytics: true,
        enableQuickCashier: true,
        enableTouchscreen: true,
        enableMultiUser: true,
        enableReports: true,
        enableBackup: true
    },

    // إعدادات الأداء
    performance: {
        enableLazyLoading: true,
        enableCaching: true,
        cacheExpiry: 3600000, // ساعة واحدة
        enableMinification: false,
        enableGzip: false,
        maxConcurrentRequests: 5
    },

    // إعدادات إمكانية الوصول
    accessibility: {
        enableHighContrast: false,
        enableLargeText: false,
        enableScreenReader: false,
        enableKeyboardNavigation: true,
        enableVoiceCommands: false
    },

    // إعدادات التحديث
    updates: {
        enableAutoUpdate: false,
        updateChannel: 'stable', // stable, beta, alpha
        checkInterval: 86400000, // 24 ساعة
        enableNotifications: true
    },

    // إعدادات التصدير والاستيراد
    dataManagement: {
        enableExport: true,
        enableImport: true,
        supportedFormats: ['json', 'csv', 'excel'],
        maxFileSize: 10, // MB
        enableValidation: true
    },

    // إعدادات الشبكة
    network: {
        enableOfflineMode: true,
        syncOnReconnect: true,
        retryAttempts: 3,
        retryDelay: 5000,
        timeout: 30000
    }
};

// دالة للحصول على إعداد معين
window.getConfig = function(path, defaultValue = null) {
    const keys = path.split('.');
    let value = window.POS_CONFIG;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

// دالة لتحديث إعداد معين
window.setConfig = function(path, newValue) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = window.POS_CONFIG;
    
    for (const key of keys) {
        if (!(key in target) || typeof target[key] !== 'object') {
            target[key] = {};
        }
        target = target[key];
    }
    
    target[lastKey] = newValue;
    
    // حفظ التكوين في التخزين المحلي
    try {
        localStorage.setItem('pos_config', JSON.stringify(window.POS_CONFIG));
    } catch (error) {
        console.warn('فشل في حفظ التكوين:', error);
    }
};

// تحميل التكوين المحفوظ
window.loadConfig = function() {
    try {
        const savedConfig = localStorage.getItem('pos_config');
        if (savedConfig) {
            const parsed = JSON.parse(savedConfig);
            // دمج التكوين المحفوظ مع التكوين الافتراضي
            window.POS_CONFIG = { ...window.POS_CONFIG, ...parsed };
        }
    } catch (error) {
        console.warn('فشل في تحميل التكوين المحفوظ:', error);
    }
};

// إعادة تعيين التكوين للقيم الافتراضية
window.resetConfig = function() {
    localStorage.removeItem('pos_config');
    location.reload();
};

// تحميل التكوين عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    window.loadConfig();
    
    // تطبيق إعدادات الواجهة
    const ui = window.getConfig('ui');
    if (ui) {
        document.documentElement.style.setProperty('--neu-primary', ui.primaryColor);
        document.documentElement.style.setProperty('--neu-secondary', ui.secondaryColor);
        document.documentElement.style.setProperty('--neu-bg', ui.backgroundColor);
        
        if (ui.direction) {
            document.documentElement.dir = ui.direction;
        }
    }
    
    // تحديد وضع الشاشة اللمسية
    const touchMode = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    window.setConfig('ui.touchMode', touchMode);
    
    if (touchMode) {
        document.body.classList.add('touch-device');
    }
});

// تصدير التكوين للاستخدام في الوحدات الأخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.POS_CONFIG;
}
