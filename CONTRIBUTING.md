# دليل المساهمة - Contributing Guide

نرحب بمساهماتكم في تطوير نظام نقطة البيع المتقدم! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 🤝 كيفية المساهمة

### 1. الإبلاغ عن الأخطاء
- تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
- استخدم قالب الإبلاغ عن الأخطاء
- قدم معلومات مفصلة عن الخطأ
- أرفق لقطات شاشة إذا أمكن

### 2. اقتراح ميزات جديدة
- ابحث في الطلبات الموجودة أولاً
- اشرح الحاجة للميزة الجديدة
- قدم أمثلة على الاستخدام
- ناقش التنفيذ المقترح

### 3. المساهمة بالكود
- Fork المشروع
- أنشئ فرع للميزة الجديدة
- اتبع معايير الكود
- اكتب اختبارات للكود الجديد
- قدم Pull Request

## 📋 معايير الكود

### JavaScript
```javascript
// استخدم const/let بدلاً من var
const apiUrl = 'https://api.example.com';
let currentUser = null;

// استخدم arrow functions عند الإمكان
const calculateTotal = (items) => {
    return items.reduce((sum, item) => sum + item.price, 0);
};

// استخدم template literals
const message = `مرحباً ${userName}، لديك ${itemCount} عنصر`;

// استخدم destructuring
const { name, price, category } = product;

// استخدم async/await بدلاً من callbacks
async function fetchData() {
    try {
        const response = await fetch(apiUrl);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
    }
}
```

### CSS
```css
/* استخدم BEM methodology */
.product-card {
    /* Block */
}

.product-card__title {
    /* Element */
}

.product-card--featured {
    /* Modifier */
}

/* استخدم CSS custom properties */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
}

/* استخدم flexbox/grid للتخطيط */
.container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}
```

### HTML
```html
<!-- استخدم semantic HTML -->
<main class="main-content">
    <section class="products-section">
        <header class="section-header">
            <h2>المنتجات</h2>
        </header>
        <article class="product-card">
            <!-- محتوى المنتج -->
        </article>
    </section>
</main>

<!-- استخدم ARIA labels للوصولية -->
<button aria-label="إضافة إلى السلة" class="add-to-cart-btn">
    <i class="bi bi-cart-plus" aria-hidden="true"></i>
</button>
```

## 🏗️ هيكل المشروع

```
web-pos/
├── css/                    # ملفات التنسيق
│   ├── neumorphic.css     # التصميم الأساسي
│   ├── main.css           # التنسيقات العامة
│   └── [module].css       # تنسيقات الوحدات
├── js/
│   ├── core/              # الوحدات الأساسية
│   │   ├── app.js         # التطبيق الرئيسي
│   │   ├── auth.js        # المصادقة
│   │   ├── storage.js     # التخزين
│   │   ├── utils.js       # الدوال المساعدة
│   │   └── notifications.js # الإشعارات
│   └── modules/           # وحدات الميزات
│       ├── dashboard.js   # لوحة التحكم
│       ├── sales.js       # المبيعات
│       └── [feature].js   # وحدات أخرى
├── index.html             # الصفحة الرئيسية
├── config.js              # ملف التكوين
└── README.md              # الوثائق
```

## 🧪 الاختبارات

### كتابة الاختبارات
```javascript
// مثال على اختبار وحدة
describe('Utils Module', () => {
    test('should format currency correctly', () => {
        const result = formatCurrency(1234.56);
        expect(result).toBe('1,234.56 ر.س');
    });

    test('should validate email format', () => {
        expect(validateEmail('<EMAIL>')).toBe(true);
        expect(validateEmail('invalid-email')).toBe(false);
    });
});
```

### تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --grep "Utils"

# تشغيل الاختبارات مع التغطية
npm run test:coverage
```

## 📝 التوثيق

### تعليقات الكود
```javascript
/**
 * حساب إجمالي الفاتورة مع الضرائب
 * @param {Array} items - قائمة العناصر
 * @param {number} taxRate - معدل الضريبة (0-1)
 * @returns {number} الإجمالي مع الضرائب
 */
function calculateTotalWithTax(items, taxRate = 0.15) {
    const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const tax = subtotal * taxRate;
    return subtotal + tax;
}
```

### JSDoc للوحدات
```javascript
/**
 * @fileoverview وحدة إدارة المبيعات
 * <AUTHOR> المطور
 * @version 1.0.0
 */

/**
 * فئة إدارة المبيعات
 * @class Sales
 */
export class Sales {
    /**
     * إنشاء مثيل جديد من Sales
     * @param {Object} app - مرجع التطبيق الرئيسي
     */
    constructor(app) {
        this.app = app;
    }
}
```

## 🎨 معايير التصميم

### الألوان
- استخدم متغيرات CSS للألوان
- اتبع نظام الألوان الموحد
- تأكد من التباين الكافي للوصولية

### التخطيط
- استخدم Grid/Flexbox للتخطيط
- اجعل التصميم متجاوب
- اتبع مبادئ Mobile-First

### الرسوم المتحركة
- استخدم CSS transitions للتفاعلات
- اجعل الرسوم المتحركة سلسة (60fps)
- قدم خيار لتعطيل الرسوم المتحركة

## 🌐 الترجمة والتدويل

### إضافة نصوص جديدة
```javascript
// استخدم مفاتيح وصفية
const messages = {
    'sales.add_to_cart': 'إضافة إلى السلة',
    'sales.remove_from_cart': 'إزالة من السلة',
    'common.save': 'حفظ',
    'common.cancel': 'إلغاء'
};
```

### دعم RTL
- استخدم logical properties
- اختبر التخطيط في كلا الاتجاهين
- تأكد من محاذاة النصوص

## 🔍 مراجعة الكود

### قائمة المراجعة
- [ ] الكود يتبع معايير المشروع
- [ ] الاختبارات تمر بنجاح
- [ ] التوثيق محدث
- [ ] لا توجد console.log في الإنتاج
- [ ] الأداء محسن
- [ ] الوصولية مراعاة
- [ ] التجاوب يعمل على جميع الأجهزة

### عملية المراجعة
1. مراجعة الكود تقنياً
2. اختبار الوظائف
3. مراجعة التصميم
4. اختبار الأداء
5. مراجعة الوثائق

## 🚀 النشر

### قبل النشر
```bash
# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint

# بناء الإنتاج
npm run build

# اختبار البناء
npm run test:build
```

### إرشادات الإصدار
- استخدم Semantic Versioning
- اكتب changelog مفصل
- اختبر على بيئات متعددة
- قم بعمل backup قبل النشر

## 📞 التواصل

### قنوات التواصل
- **GitHub Issues**: للأخطاء والطلبات
- **GitHub Discussions**: للنقاشات العامة
- **Email**: <EMAIL>
- **Discord**: [رابط الخادم]

### آداب التواصل
- كن محترماً ومهذباً
- استخدم اللغة المناسبة
- قدم معلومات واضحة
- اتبع قوالب الطلبات

## 🏆 الاعتراف بالمساهمين

نقدر جميع المساهمات، مهما كانت صغيرة. سيتم إضافة أسماء المساهمين إلى:
- ملف README.md
- صفحة الشكر في التطبيق
- سجل المساهمين

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على ترخيص مساهمتك تحت نفس ترخيص المشروع (MIT License).

---

شكراً لك على اهتمامك بالمساهمة في نظام نقطة البيع المتقدم! 🙏
