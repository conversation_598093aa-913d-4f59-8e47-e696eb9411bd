# 💿 دليل التثبيت - Installation Guide

دليل شامل لتثبيت نظام نقطة البيع المتقدم على جميع أنظمة التشغيل

## 🎯 خيارات التثبيت

### 1. 🖥️ تطبيق سطح المكتب (الموصى به)
- يعمل بدون متصفح
- أداء أفضل وأمان أعلى
- ميزات إضافية متقدمة

### 2. 🌐 تطبيق ويب
- يعمل في المتصفح
- سهل التشغيل
- لا يحتاج تثبيت

## 🖥️ تثبيت تطبيق سطح المكتب

### Windows

#### الطريقة الأولى: استخدام المثبت (الأسهل)
1. **تحميل المثبت**:
   - حمّل `Advanced POS Setup 1.0.0.exe`
   - تأكد من أن الملف من مصدر موثوق

2. **تشغيل المثبت**:
   - انقر نقراً مزدوجاً على ملف المثبت
   - اتبع التعليمات على الشاشة
   - اختر مجلد التثبيت (افتراضي: `C:\Program Files\نظام نقطة البيع المتقدم`)

3. **إكمال التثبيت**:
   - انتظر حتى انتهاء التثبيت
   - اختر إنشاء اختصار على سطح المكتب
   - انقر "إنهاء"

#### الطريقة الثانية: النسخة المحمولة
1. **تحميل النسخة المحمولة**:
   - **64-bit**: `AdvancedPOS-Portable-1.0.0-x64.exe`
   - **32-bit**: `AdvancedPOS-Portable-1.0.0-ia32.exe`

2. **التشغيل**:
   - ضع الملف في أي مجلد
   - انقر نقراً مزدوجاً للتشغيل
   - لا يحتاج تثبيت

#### متطلبات Windows
- **نظام التشغيل**: Windows 7/8/10/11
- **المعمارية**: 32-bit أو 64-bit
- **الذاكرة**: 2 GB RAM (4 GB موصى به)
- **التخزين**: 500 MB مساحة فارغة

### macOS

#### التثبيت
1. **تحميل الملف**:
   - حمّل `Advanced POS-1.0.0.dmg`

2. **التثبيت**:
   - انقر نقراً مزدوجاً على ملف DMG
   - اسحب التطبيق إلى مجلد Applications
   - أغلق نافذة DMG

3. **التشغيل**:
   - افتح Launchpad أو Applications
   - ابحث عن "نظام نقطة البيع المتقدم"
   - انقر للتشغيل

#### متطلبات macOS
- **نظام التشغيل**: macOS 10.12 أو أحدث
- **المعمارية**: Intel أو Apple Silicon
- **الذاكرة**: 2 GB RAM (4 GB موصى به)
- **التخزين**: 500 MB مساحة فارغة

### Linux

#### Ubuntu/Debian
```bash
# تحميل حزمة DEB
wget https://releases.com/advanced-pos_1.0.0_amd64.deb

# تثبيت الحزمة
sudo dpkg -i advanced-pos_1.0.0_amd64.deb

# إصلاح التبعيات إذا لزم الأمر
sudo apt-get install -f
```

#### Fedora/CentOS/RHEL
```bash
# تحميل حزمة RPM
wget https://releases.com/advanced-pos-1.0.0.x86_64.rpm

# تثبيت الحزمة
sudo rpm -i advanced-pos-1.0.0.x86_64.rpm

# أو باستخدام dnf
sudo dnf install advanced-pos-1.0.0.x86_64.rpm
```

#### AppImage (جميع توزيعات Linux)
```bash
# تحميل AppImage
wget https://releases.com/Advanced_POS-1.0.0.AppImage

# جعل الملف قابل للتنفيذ
chmod +x Advanced_POS-1.0.0.AppImage

# تشغيل التطبيق
./Advanced_POS-1.0.0.AppImage
```

#### متطلبات Linux
- **نظام التشغيل**: Ubuntu 18.04+, Debian 10+, Fedora 30+, CentOS 8+
- **المعمارية**: x64
- **الذاكرة**: 2 GB RAM (4 GB موصى به)
- **التخزين**: 500 MB مساحة فارغة

## 🌐 تثبيت تطبيق الويب

### المتطلبات
- خادم ويب (Python, Node.js, PHP, Apache, Nginx)
- متصفح حديث

### الطريقة الأولى: Python (الأسهل)
```bash
# تحميل المشروع
git clone https://github.com/your-repo/web-pos.git
cd web-pos

# تشغيل الخادم
python -m http.server 8000

# فتح المتصفح
http://localhost:8000
```

### الطريقة الثانية: Node.js
```bash
# تثبيت http-server
npm install -g http-server

# تشغيل الخادم
http-server -p 8000

# فتح المتصفح
http://localhost:8000
```

### الطريقة الثالثة: PHP
```bash
# تشغيل خادم PHP المدمج
php -S localhost:8000

# فتح المتصفح
http://localhost:8000
```

### الطريقة الرابعة: Apache/Nginx
1. انسخ ملفات المشروع إلى مجلد الخادم
2. تأكد من تكوين الخادم بشكل صحيح
3. افتح المتصفح وانتقل إلى عنوان الخادم

## 🔧 التكوين الأولي

### تسجيل الدخول الأول
- **المدير**: `admin` / `admin123`
- **الكاشير**: `cashier` / `cashier123`

### إعداد الشركة
1. انتقل إلى **الإعدادات**
2. تبويب **معلومات الشركة**
3. أدخل:
   - اسم الشركة
   - العنوان
   - رقم الهاتف
   - البريد الإلكتروني
   - الرقم الضريبي

### إعداد النظام
1. تبويب **إعدادات النظام**
2. تأكد من:
   - العملة: الدينار الجزائري (دج)
   - معدل الضريبة: 19%
   - إعدادات الطباعة

### إضافة البيانات الأولية
1. **إضافة فئات المنتجات**
2. **إضافة المنتجات الأساسية**
3. **إضافة الموردين**
4. **إنشاء حسابات المستخدمين**

## 🔒 الأمان

### كلمات المرور
- غيّر كلمات المرور الافتراضية فوراً
- استخدم كلمات مرور قوية
- لا تشارك معلومات تسجيل الدخول

### النسخ الاحتياطية
- أنشئ نسخة احتياطية فور الانتهاء من الإعداد
- جدولة نسخ احتياطية دورية
- احفظ النسخ في مكان آمن

### تحديثات الأمان
- تحقق من التحديثات بانتظام
- طبق تصحيحات الأمان فوراً
- راقب سجل الأنشطة

## 🔧 حل مشاكل التثبيت

### Windows

#### مشكلة: "Windows protected your PC"
1. انقر "More info"
2. انقر "Run anyway"
3. أو: انقر بالزر الأيمن → Properties → Unblock

#### مشكلة: فشل التثبيت
1. تشغيل المثبت كمدير
2. تعطيل برنامج مكافحة الفيروسات مؤقتاً
3. تحقق من مساحة القرص الصلب

### macOS

#### مشكلة: "App can't be opened"
1. System Preferences → Security & Privacy
2. انقر "Open Anyway"
3. أو: `sudo xattr -rd com.apple.quarantine /Applications/AdvancedPOS.app`

#### مشكلة: فشل التثبيت
1. تحقق من إصدار macOS
2. تحقق من مساحة القرص
3. أعد تشغيل الجهاز وحاول مرة أخرى

### Linux

#### مشكلة: تبعيات مفقودة
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -f

# Fedora
sudo dnf install missing-package-name
```

#### مشكلة: صلاحيات
```bash
# جعل الملف قابل للتنفيذ
chmod +x filename

# تشغيل بصلاحيات المدير
sudo ./filename
```

## 📞 الدعم

### الحصول على المساعدة
- راجع [دليل المستخدم](USER_GUIDE.md)
- تحقق من [الأسئلة الشائعة](FAQ.md)
- ابحث في [GitHub Issues](https://github.com/your-repo/issues)

### الإبلاغ عن مشاكل
عند الإبلاغ عن مشكلة، أرفق:
- نظام التشغيل والإصدار
- طريقة التثبيت المستخدمة
- رسالة الخطأ الكاملة
- خطوات إعادة الإنتاج

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **GitHub**: https://github.com/your-repo/web-pos
- **الوثائق**: https://docs.pos-system.com

---

**تهانينا!** 🎉 أصبح نظام نقطة البيع المتقدم جاهزاً للاستخدام!
