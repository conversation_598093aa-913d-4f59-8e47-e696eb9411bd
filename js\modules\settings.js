/**
 * وحدة الإعدادات
 * Settings Module
 */

export class Settings {
    constructor(app) {
        this.app = app;
        this.storage = app.storage;
        this.utils = app.utils;
        this.notifications = app.notifications;
        
        this.settings = {};
    }

    /**
     * عرض واجهة الإعدادات
     */
    async render() {
        try {
            await this.loadSettings();
            this.createSettingsInterface();
            this.bindEvents();
            this.loadSettingsData();
        } catch (error) {
            console.error('خطأ في عرض واجهة الإعدادات:', error);
            this.notifications.error('حدث خطأ في تحميل واجهة الإعدادات');
        }
    }

    /**
     * تحميل الإعدادات
     */
    async loadSettings() {
        this.settings = this.storage.get('settings') || {};
    }

    /**
     * إنشاء واجهة الإعدادات
     */
    createSettingsInterface() {
        const container = document.getElementById('settings-tab');
        if (!container) return;

        container.innerHTML = `
            <div class="settings-container">
                <div class="settings-header neumorphic-card">
                    <h2><i class="bi bi-gear"></i> الإعدادات</h2>
                    <div class="settings-actions">
                        <button class="btn neumorphic-btn btn-success" onclick="settingsModule.saveSettings()">
                            <i class="bi bi-check-circle"></i>
                            حفظ الإعدادات
                        </button>
                        <button class="btn neumorphic-btn" onclick="settingsModule.resetSettings()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>

                <div class="settings-tabs neumorphic-card">
                    <ul class="nav nav-tabs" id="settingsTabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#companySettings">
                                <i class="bi bi-building"></i>
                                معلومات الشركة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#posSettings">
                                <i class="bi bi-shop"></i>
                                إعدادات النظام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#displaySettings">
                                <i class="bi bi-display"></i>
                                إعدادات العرض
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#usersSettings">
                                <i class="bi bi-people"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#backupSettings">
                                <i class="bi bi-cloud-download"></i>
                                النسخ الاحتياطية
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content" id="settingsTabContent">
                        <!-- Company Settings -->
                        <div class="tab-pane fade show active" id="companySettings">
                            <div class="settings-section">
                                <h4>معلومات الشركة</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الشركة</label>
                                            <input type="text" id="companyName" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم السجل التجاري</label>
                                            <input type="text" id="commercialRegister" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" id="taxNumber" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="tel" id="companyPhone" class="form-control neumorphic-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" id="companyEmail" class="form-control neumorphic-input">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea id="companyAddress" class="form-control neumorphic-input" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- POS Settings -->
                        <div class="tab-pane fade" id="posSettings">
                            <div class="settings-section">
                                <h4>إعدادات نقطة البيع</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">العملة</label>
                                            <input type="text" id="currency" class="form-control neumorphic-input" value="دينار جزائري">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رمز العملة</label>
                                            <input type="text" id="currencySymbol" class="form-control neumorphic-input" value="دج">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">معدل الضريبة (%)</label>
                                            <input type="number" id="taxRate" class="form-control neumorphic-input"
                                                   step="0.01" min="0" max="100" value="19">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع الافتراضية</label>
                                            <select id="defaultPaymentMethod" class="form-select neumorphic-select">
                                                <option value="cash">نقدي</option>
                                                <option value="card">بطاقة ائتمان</option>
                                                <option value="transfer">تحويل بنكي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نص ذيل الفاتورة</label>
                                    <textarea id="receiptFooter" class="form-control neumorphic-input" rows="2"></textarea>
                                </div>
                                <div class="settings-checkboxes">
                                    <div class="form-check">
                                        <input type="checkbox" id="autoBackup" class="form-check-input neumorphic-checkbox">
                                        <label class="form-check-label" for="autoBackup">
                                            النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" id="soundEnabled" class="form-check-input neumorphic-checkbox">
                                        <label class="form-check-label" for="soundEnabled">
                                            تفعيل الأصوات
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" id="printReceipt" class="form-check-input neumorphic-checkbox">
                                        <label class="form-check-label" for="printReceipt">
                                            طباعة الفاتورة تلقائياً
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Display Settings -->
                        <div class="tab-pane fade" id="displaySettings">
                            <div class="settings-section">
                                <h4>إعدادات العرض</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المظهر</label>
                                            <select id="theme" class="form-select neumorphic-select">
                                                <option value="neumorphic">Neumorphic</option>
                                                <option value="classic">كلاسيكي</option>
                                                <option value="dark">داكن</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اللغة</label>
                                            <select id="language" class="form-select neumorphic-select">
                                                <option value="ar">العربية</option>
                                                <option value="en">English</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تنسيق التاريخ</label>
                                            <select id="dateFormat" class="form-select neumorphic-select">
                                                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تنسيق الوقت</label>
                                            <select id="timeFormat" class="form-select neumorphic-select">
                                                <option value="24h">24 ساعة</option>
                                                <option value="12h">12 ساعة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Users Settings -->
                        <div class="tab-pane fade" id="usersSettings">
                            <div class="settings-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4>إدارة المستخدمين</h4>
                                    <button class="btn neumorphic-btn btn-primary" onclick="settingsModule.showAddUserModal()">
                                        <i class="bi bi-person-plus"></i>
                                        إضافة مستخدم
                                    </button>
                                </div>
                                <div id="usersList" class="users-list">
                                    <!-- Users will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Backup Settings -->
                        <div class="tab-pane fade" id="backupSettings">
                            <div class="settings-section">
                                <h4>النسخ الاحتياطية</h4>
                                <div class="backup-actions">
                                    <div class="backup-item neumorphic-card">
                                        <div class="backup-info">
                                            <h5><i class="bi bi-download"></i> تصدير البيانات</h5>
                                            <p>تصدير جميع بيانات النظام كنسخة احتياطية</p>
                                        </div>
                                        <button class="btn neumorphic-btn btn-primary" onclick="settingsModule.exportData()">
                                            <i class="bi bi-download"></i>
                                            تصدير
                                        </button>
                                    </div>
                                    
                                    <div class="backup-item neumorphic-card">
                                        <div class="backup-info">
                                            <h5><i class="bi bi-upload"></i> استيراد البيانات</h5>
                                            <p>استيراد البيانات من نسخة احتياطية</p>
                                        </div>
                                        <button class="btn neumorphic-btn btn-warning" onclick="settingsModule.importData()">
                                            <i class="bi bi-upload"></i>
                                            استيراد
                                        </button>
                                    </div>
                                    
                                    <div class="backup-item neumorphic-card">
                                        <div class="backup-info">
                                            <h5><i class="bi bi-trash"></i> مسح جميع البيانات</h5>
                                            <p>حذف جميع البيانات وإعادة تعيين النظام</p>
                                        </div>
                                        <button class="btn neumorphic-btn btn-danger" onclick="settingsModule.clearAllData()">
                                            <i class="bi bi-trash"></i>
                                            مسح الكل
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="storage-info neumorphic-card mt-4">
                                    <h5><i class="bi bi-hdd"></i> معلومات التخزين</h5>
                                    <div id="storageStats">
                                        <!-- Storage stats will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        window.settingsModule = this;
    }

    /**
     * تحميل بيانات الإعدادات
     */
    loadSettingsData() {
        // معلومات الشركة
        if (this.settings.company) {
            document.getElementById('companyName').value = this.settings.company.name || '';
            document.getElementById('commercialRegister').value = this.settings.company.commercialRegister || '';
            document.getElementById('taxNumber').value = this.settings.company.taxNumber || '';
            document.getElementById('companyPhone').value = this.settings.company.phone || '';
            document.getElementById('companyEmail').value = this.settings.company.email || '';
            document.getElementById('companyAddress').value = this.settings.company.address || '';
        }

        // إعدادات النظام
        if (this.settings.pos) {
            document.getElementById('currency').value = this.settings.pos.currency || 'دينار جزائري';
            document.getElementById('currencySymbol').value = this.settings.pos.currencySymbol || 'دج';
            document.getElementById('taxRate').value = (this.settings.pos.taxRate || 0.19) * 100;
            document.getElementById('defaultPaymentMethod').value = this.settings.pos.defaultPaymentMethod || 'cash';
            document.getElementById('receiptFooter').value = this.settings.pos.receiptFooter || '';
            document.getElementById('autoBackup').checked = this.settings.pos.autoBackup || false;
            document.getElementById('soundEnabled').checked = this.settings.pos.soundEnabled || true;
            document.getElementById('printReceipt').checked = this.settings.pos.printReceipt || true;
        }

        // إعدادات العرض
        if (this.settings.display) {
            document.getElementById('theme').value = this.settings.display.theme || 'neumorphic';
            document.getElementById('language').value = this.settings.display.language || 'ar';
            document.getElementById('dateFormat').value = this.settings.display.dateFormat || 'DD/MM/YYYY';
            document.getElementById('timeFormat').value = this.settings.display.timeFormat || '24h';
        }

        // تحميل المستخدمين
        this.loadUsersList();
        
        // تحميل إحصائيات التخزين
        this.loadStorageStats();
    }

    /**
     * حفظ الإعدادات
     */
    async saveSettings() {
        try {
            const settings = {
                company: {
                    name: document.getElementById('companyName').value,
                    commercialRegister: document.getElementById('commercialRegister').value,
                    taxNumber: document.getElementById('taxNumber').value,
                    phone: document.getElementById('companyPhone').value,
                    email: document.getElementById('companyEmail').value,
                    address: document.getElementById('companyAddress').value
                },
                pos: {
                    currency: document.getElementById('currency').value,
                    currencySymbol: document.getElementById('currencySymbol').value,
                    taxRate: parseFloat(document.getElementById('taxRate').value) / 100,
                    defaultPaymentMethod: document.getElementById('defaultPaymentMethod').value,
                    receiptFooter: document.getElementById('receiptFooter').value,
                    autoBackup: document.getElementById('autoBackup').checked,
                    soundEnabled: document.getElementById('soundEnabled').checked,
                    printReceipt: document.getElementById('printReceipt').checked
                },
                display: {
                    theme: document.getElementById('theme').value,
                    language: document.getElementById('language').value,
                    dateFormat: document.getElementById('dateFormat').value,
                    timeFormat: document.getElementById('timeFormat').value
                }
            };

            this.storage.set('settings', settings);
            this.settings = settings;
            
            this.notifications.success('تم حفظ الإعدادات بنجاح');
            
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            this.notifications.error('حدث خطأ في حفظ الإعدادات');
        }
    }

    /**
     * إعادة تعيين الإعدادات
     */
    async resetSettings() {
        const confirmed = await this.notifications.confirm(
            'هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟',
            'تأكيد إعادة التعيين'
        );

        if (confirmed) {
            this.storage.remove('settings');
            await this.loadSettings();
            this.loadSettingsData();
            this.notifications.success('تم إعادة تعيين الإعدادات');
        }
    }

    /**
     * تحميل قائمة المستخدمين
     */
    loadUsersList() {
        const users = this.app.auth.getUsers();
        const container = document.getElementById('usersList');
        
        if (!container) return;

        if (users.length === 0) {
            container.innerHTML = '<p class="text-muted">لا توجد مستخدمين</p>';
            return;
        }

        container.innerHTML = users.map(user => `
            <div class="user-item neumorphic-card">
                <div class="user-info">
                    <h6>${user.fullName}</h6>
                    <p class="text-muted">${user.username} - ${this.getRoleName(user.role)}</p>
                </div>
                <div class="user-actions">
                    <button class="btn btn-sm neumorphic-btn" onclick="settingsModule.editUser(${user.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm neumorphic-btn btn-danger" onclick="settingsModule.deleteUser(${user.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحميل إحصائيات التخزين
     */
    loadStorageStats() {
        const stats = this.storage.getStats();
        const container = document.getElementById('storageStats');
        
        if (!container) return;

        container.innerHTML = `
            <div class="storage-stat">
                <span>عدد المفاتيح:</span>
                <span>${stats.totalKeys}</span>
            </div>
            <div class="storage-stat">
                <span>حجم البيانات:</span>
                <span>${this.utils.formatBytes(stats.totalSize)}</span>
            </div>
            <div class="storage-stat">
                <span>نوع التخزين:</span>
                <span>${stats.isSupported ? 'localStorage' : 'ذاكرة مؤقتة'}</span>
            </div>
        `;
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        this.app.exportData();
    }

    /**
     * استيراد البيانات
     */
    importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (file) {
                await this.app.importData(file);
                this.loadSettingsData();
            }
        };

        input.click();
    }

    /**
     * مسح جميع البيانات
     */
    async clearAllData() {
        const confirmed = await this.notifications.confirm(
            'هل تريد حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!',
            'تأكيد الحذف',
            {
                confirmText: 'نعم، احذف الكل',
                cancelText: 'إلغاء',
                type: 'error'
            }
        );

        if (confirmed) {
            this.storage.clear();
            this.notifications.success('تم حذف جميع البيانات');
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
    }

    /**
     * الحصول على اسم الدور
     */
    getRoleName(role) {
        const roles = {
            'admin': 'مدير',
            'manager': 'مدير مساعد',
            'cashier': 'كاشير'
        };
        return roles[role] || role;
    }

    /**
     * إعادة تحميل البيانات
     */
    reload() {
        this.loadSettings();
        this.loadSettingsData();
    }
}
