/**
 * نظام التخزين المحلي
 * Local Storage System
 */

export class Storage {
    constructor() {
        this.prefix = 'pos_';
        this.compressionEnabled = true;
        this.encryptionEnabled = false; // يمكن تفعيلها لاحقاً
        
        // فحص دعم localStorage
        this.isSupported = this.checkSupport();
        
        if (!this.isSupported) {
            console.warn('localStorage غير مدعوم، سيتم استخدام ذاكرة مؤقتة');
            this.fallbackStorage = new Map();
        }
    }

    /**
     * فحص دعم localStorage
     */
    checkSupport() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * حفظ البيانات
     */
    set(key, value) {
        try {
            const fullKey = this.prefix + key;
            let dataToStore = value;
            
            // تحويل البيانات إلى JSON
            if (typeof value !== 'string') {
                dataToStore = JSON.stringify(value);
            }
            
            // ضغط البيانات إذا كانت كبيرة
            if (this.compressionEnabled && dataToStore.length > 1000) {
                dataToStore = this.compress(dataToStore);
            }
            
            // تشفير البيانات إذا كان مفعلاً
            if (this.encryptionEnabled) {
                dataToStore = this.encrypt(dataToStore);
            }
            
            if (this.isSupported) {
                localStorage.setItem(fullKey, dataToStore);
            } else {
                this.fallbackStorage.set(fullKey, dataToStore);
            }
            
            // تسجيل آخر تحديث
            this.setLastModified(key);
            
            return true;
            
        } catch (error) {
            console.error(`خطأ في حفظ البيانات للمفتاح ${key}:`, error);
            
            // محاولة تنظيف التخزين إذا امتلأ
            if (error.name === 'QuotaExceededError') {
                this.cleanup();
                // محاولة أخرى
                try {
                    const fullKey = this.prefix + key;
                    if (this.isSupported) {
                        localStorage.setItem(fullKey, dataToStore);
                    } else {
                        this.fallbackStorage.set(fullKey, dataToStore);
                    }
                    return true;
                } catch (retryError) {
                    console.error('فشل في حفظ البيانات حتى بعد التنظيف:', retryError);
                }
            }
            
            return false;
        }
    }

    /**
     * استرجاع البيانات
     */
    get(key, defaultValue = null) {
        try {
            const fullKey = this.prefix + key;
            let data;
            
            if (this.isSupported) {
                data = localStorage.getItem(fullKey);
            } else {
                data = this.fallbackStorage.get(fullKey);
            }
            
            if (data === null || data === undefined) {
                return defaultValue;
            }
            
            // فك التشفير إذا كان مفعلاً
            if (this.encryptionEnabled) {
                data = this.decrypt(data);
            }
            
            // فك الضغط إذا كان مضغوطاً
            if (this.compressionEnabled && this.isCompressed(data)) {
                data = this.decompress(data);
            }
            
            // محاولة تحويل من JSON
            try {
                return JSON.parse(data);
            } catch (parseError) {
                // إذا فشل التحويل، إرجاع البيانات كما هي
                return data;
            }
            
        } catch (error) {
            console.error(`خطأ في استرجاع البيانات للمفتاح ${key}:`, error);
            return defaultValue;
        }
    }

    /**
     * حذف البيانات
     */
    remove(key) {
        try {
            const fullKey = this.prefix + key;
            
            if (this.isSupported) {
                localStorage.removeItem(fullKey);
            } else {
                this.fallbackStorage.delete(fullKey);
            }
            
            // حذف معلومات آخر تحديث
            this.removeLastModified(key);
            
            return true;
            
        } catch (error) {
            console.error(`خطأ في حذف البيانات للمفتاح ${key}:`, error);
            return false;
        }
    }

    /**
     * فحص وجود المفتاح
     */
    has(key) {
        try {
            const fullKey = this.prefix + key;
            
            if (this.isSupported) {
                return localStorage.getItem(fullKey) !== null;
            } else {
                return this.fallbackStorage.has(fullKey);
            }
            
        } catch (error) {
            console.error(`خطأ في فحص وجود المفتاح ${key}:`, error);
            return false;
        }
    }

    /**
     * مسح جميع البيانات
     */
    clear() {
        try {
            if (this.isSupported) {
                // حذف المفاتيح التي تبدأ بالبادئة فقط
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(this.prefix)) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => localStorage.removeItem(key));
            } else {
                // مسح الذاكرة المؤقتة
                const keysToRemove = [];
                for (const key of this.fallbackStorage.keys()) {
                    if (key.startsWith(this.prefix)) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => this.fallbackStorage.delete(key));
            }
            
            return true;
            
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }

    /**
     * الحصول على جميع المفاتيح
     */
    keys() {
        try {
            const keys = [];
            
            if (this.isSupported) {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(this.prefix)) {
                        keys.push(key.substring(this.prefix.length));
                    }
                }
            } else {
                for (const key of this.fallbackStorage.keys()) {
                    if (key.startsWith(this.prefix)) {
                        keys.push(key.substring(this.prefix.length));
                    }
                }
            }
            
            return keys;
            
        } catch (error) {
            console.error('خطأ في الحصول على المفاتيح:', error);
            return [];
        }
    }

    /**
     * الحصول على حجم البيانات المخزنة
     */
    getSize() {
        try {
            let totalSize = 0;
            
            if (this.isSupported) {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith(this.prefix)) {
                        const value = localStorage.getItem(key);
                        totalSize += key.length + (value ? value.length : 0);
                    }
                }
            } else {
                for (const [key, value] of this.fallbackStorage.entries()) {
                    if (key.startsWith(this.prefix)) {
                        totalSize += key.length + (value ? value.length : 0);
                    }
                }
            }
            
            return totalSize;
            
        } catch (error) {
            console.error('خطأ في حساب حجم البيانات:', error);
            return 0;
        }
    }

    /**
     * تنظيف البيانات القديمة
     */
    cleanup() {
        try {
            const keys = this.keys();
            const now = Date.now();
            const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 يوم
            
            keys.forEach(key => {
                const lastModified = this.getLastModified(key);
                if (lastModified && (now - lastModified) > maxAge) {
                    // حذف البيانات القديمة (باستثناء البيانات المهمة)
                    if (!this.isImportantKey(key)) {
                        this.remove(key);
                    }
                }
            });
            
            console.log('تم تنظيف البيانات القديمة');
            
        } catch (error) {
            console.error('خطأ في تنظيف البيانات:', error);
        }
    }

    /**
     * نسخ احتياطي من البيانات
     */
    backup() {
        try {
            const backup = {};
            const keys = this.keys();
            
            keys.forEach(key => {
                backup[key] = this.get(key);
            });
            
            backup._timestamp = new Date().toISOString();
            backup._version = '1.0';
            
            return backup;
            
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return null;
        }
    }

    /**
     * استعادة من النسخة الاحتياطية
     */
    restore(backup) {
        try {
            if (!backup || typeof backup !== 'object') {
                throw new Error('النسخة الاحتياطية غير صحيحة');
            }
            
            // مسح البيانات الحالية
            this.clear();
            
            // استعادة البيانات
            Object.keys(backup).forEach(key => {
                if (!key.startsWith('_')) { // تجاهل البيانات الوصفية
                    this.set(key, backup[key]);
                }
            });
            
            console.log('تم استعادة البيانات من النسخة الاحتياطية');
            return true;
            
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    /**
     * ضغط البيانات (تنفيذ بسيط)
     */
    compress(data) {
        // تنفيذ بسيط للضغط - يمكن تحسينه لاحقاً
        return '__compressed__' + btoa(data);
    }

    /**
     * فك ضغط البيانات
     */
    decompress(data) {
        if (this.isCompressed(data)) {
            return atob(data.substring('__compressed__'.length));
        }
        return data;
    }

    /**
     * فحص إذا كانت البيانات مضغوطة
     */
    isCompressed(data) {
        return typeof data === 'string' && data.startsWith('__compressed__');
    }

    /**
     * تشفير البيانات (تنفيذ بسيط)
     */
    encrypt(data) {
        // تنفيذ بسيط للتشفير - يجب استخدام مكتبة تشفير قوية في الإنتاج
        return '__encrypted__' + btoa(data);
    }

    /**
     * فك تشفير البيانات
     */
    decrypt(data) {
        if (typeof data === 'string' && data.startsWith('__encrypted__')) {
            return atob(data.substring('__encrypted__'.length));
        }
        return data;
    }

    /**
     * تسجيل آخر تحديث
     */
    setLastModified(key) {
        const metaKey = `_meta_${key}`;
        const meta = {
            lastModified: Date.now(),
            version: 1
        };
        
        if (this.isSupported) {
            localStorage.setItem(this.prefix + metaKey, JSON.stringify(meta));
        } else {
            this.fallbackStorage.set(this.prefix + metaKey, JSON.stringify(meta));
        }
    }

    /**
     * الحصول على آخر تحديث
     */
    getLastModified(key) {
        try {
            const metaKey = `_meta_${key}`;
            let meta;
            
            if (this.isSupported) {
                meta = localStorage.getItem(this.prefix + metaKey);
            } else {
                meta = this.fallbackStorage.get(this.prefix + metaKey);
            }
            
            if (meta) {
                const parsed = JSON.parse(meta);
                return parsed.lastModified;
            }
            
            return null;
            
        } catch (error) {
            return null;
        }
    }

    /**
     * حذف معلومات آخر تحديث
     */
    removeLastModified(key) {
        const metaKey = `_meta_${key}`;
        
        if (this.isSupported) {
            localStorage.removeItem(this.prefix + metaKey);
        } else {
            this.fallbackStorage.delete(this.prefix + metaKey);
        }
    }

    /**
     * فحص إذا كان المفتاح مهماً (لا يجب حذفه في التنظيف)
     */
    isImportantKey(key) {
        const importantKeys = [
            'users',
            'settings',
            'products',
            'customers',
            'suppliers',
            'currentSession'
        ];
        
        return importantKeys.includes(key);
    }

    /**
     * الحصول على إحصائيات التخزين
     */
    getStats() {
        return {
            totalKeys: this.keys().length,
            totalSize: this.getSize(),
            isSupported: this.isSupported,
            compressionEnabled: this.compressionEnabled,
            encryptionEnabled: this.encryptionEnabled
        };
    }
}
