# 🖥️ إعداد تطبيق سطح المكتب - Desktop Setup

دليل سريع لتحويل نظام نقطة البيع إلى تطبيق سطح مكتب يعمل على Windows 32-bit و 64-bit.

## ⚡ البدء السريع

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من: https://nodejs.org/
- اختر النسخة LTS (الموصى بها)
- تأكد من تحديد خيار "Add to PATH" أثناء التثبيت

### 2. تثبيت التبعيات
```bash
# افتح Command Prompt أو PowerShell
cd web-pos
npm install
```

### 3. تشغيل التطبيق في وضع التطوير
```bash
npm run electron-dev
```

### 4. بناء التطبيق للتوزيع
```bash
# بناء لنظام Windows (32-bit و 64-bit)
npm run build-win

# أو بناء لجميع المنصات
npm run build-all
```

## 📁 الملفات المُنتجة

بعد البناء، ستجد الملفات في مجلد `dist/`:

### Windows
- `Advanced POS Setup 1.0.0.exe` - مثبت كامل
- `AdvancedPOS-Portable-1.0.0-x64.exe` - نسخة محمولة 64-bit
- `AdvancedPOS-Portable-1.0.0-ia32.exe` - نسخة محمولة 32-bit

## 🎯 الميزات الجديدة في تطبيق سطح المكتب

### ✅ ما تم إضافته:
- 🖥️ **تطبيق سطح مكتب أصلي** - يعمل بدون متصفح
- 📱 **شاشة بداية جميلة** - مع رسوم متحركة
- 🎛️ **قائمة تطبيق كاملة** - ملف، تحرير، عرض، مساعدة
- 💾 **حوارات حفظ/فتح** - لاستيراد وتصدير البيانات
- 🔒 **أمان محسن** - بيئة معزولة وآمنة
- ⌨️ **اختصارات لوحة المفاتيح** - للوصول السريع
- 🖨️ **دعم الطباعة** - طباعة الفواتير والتقارير
- 📊 **تصدير متقدم** - PDF, Excel, CSV
- 🔄 **نسخ احتياطية** - حفظ واستعادة البيانات
- 🎨 **أيقونة مخصصة** - في شريط المهام وسطح المكتب

### 🎛️ القوائم والاختصارات:

#### قائمة ملف
- `Ctrl+N` - جديد
- `Ctrl+O` - فتح
- `Ctrl+S` - حفظ
- `Ctrl+Q` - خروج

#### قائمة عرض
- `Ctrl+R` - إعادة تحميل
- `Ctrl+Shift+I` - أدوات المطور (وضع التطوير)
- `Ctrl++` - تكبير
- `Ctrl+-` - تصغير
- `Ctrl+0` - الحجم الطبيعي
- `F11` - ملء الشاشة

## 🔧 إعدادات متقدمة

### تخصيص الأيقونة
1. ضع أيقونة PNG (512x512) في `assets/icon.png`
2. استخدم أدوات التحويل لإنشاء:
   - `assets/icon.ico` (Windows)
   - `assets/icon.icns` (macOS)

### تخصيص المثبت
عدّل ملف `installer.nsh` لتخصيص:
- رسائل المثبت
- الاختصارات
- تسجيل أنواع الملفات

### تخصيص معلومات التطبيق
عدّل `package.json` في قسم `build`:
```json
{
  "build": {
    "appId": "com.yourcompany.pos",
    "productName": "اسم شركتك - نقطة البيع",
    "copyright": "حقوق الطبع والنشر © 2024 شركتك"
  }
}
```

## 🚀 التوزيع

### للاستخدام الشخصي
- استخدم النسخة المحمولة (Portable)
- لا تحتاج تثبيت، شغّل مباشرة

### للتوزيع التجاري
- استخدم المثبت (Setup.exe)
- فكر في التوقيع الرقمي للثقة
- اختبر على أجهزة مختلفة

## 🔍 اختبار التطبيق

### اختبار أساسي
1. تشغيل التطبيق
2. تسجيل الدخول
3. إضافة منتج
4. إجراء عملية بيع
5. طباعة فاتورة
6. إنشاء تقرير

### اختبار متقدم
1. استيراد/تصدير البيانات
2. النسخ الاحتياطية
3. الاختصارات
4. القوائم
5. إعدادات النظام

## 🐛 حل المشاكل

### التطبيق لا يبدأ
```bash
# تحقق من سجلات الأخطاء
npm run electron-dev
```

### مشاكل البناء
```bash
# امسح وأعد التثبيت
rm -rf node_modules
npm install
npm run build-win
```

### مشاكل الأداء
- تأكد من وجود ذاكرة كافية (4GB+)
- أغلق التطبيقات الأخرى أثناء البناء
- استخدم SSD إذا أمكن

## 📞 الدعم

### الحصول على المساعدة
- راجع ملف `BUILD.md` للتفاصيل الكاملة
- تحقق من سجلات الأخطاء
- ابحث في GitHub Issues

### الإبلاغ عن مشاكل
عند الإبلاغ عن مشكلة، أرفق:
- نظام التشغيل والإصدار
- إصدار Node.js
- رسالة الخطأ الكاملة
- خطوات إعادة الإنتاج

## 🎉 تهانينا!

أصبح لديك الآن تطبيق سطح مكتب كامل لنظام نقطة البيع!

### الخطوات التالية:
1. اختبر التطبيق بدقة
2. خصص الأيقونة والمعلومات
3. وزع على الأجهزة المطلوبة
4. درب المستخدمين
5. استمتع بالنظام الجديد!

---

**ملاحظة**: هذا التطبيق يدعم Windows 7/8/10/11 في إصدارات 32-bit و 64-bit.
